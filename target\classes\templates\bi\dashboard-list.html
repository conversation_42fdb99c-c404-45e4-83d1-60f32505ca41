<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胜大科技智联管理系统 - BI大屏管理</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/bootstrap-icons.min.css" rel="stylesheet">
    <link href="/css/button-styles.css" rel="stylesheet">
    <style>
        .dashboard-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .dashboard-preview {
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        .btn-group-actions {
            gap: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div th:replace="fragments/navbar :: navbar('胜大科技智联管理系统 - BI大屏管理', 'biDashboard', true, true, true, null)"></div>

    <div class="container-fluid">
        <!-- 页面标题和操作按钮 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h2><i class="bi bi-bar-chart"></i> BI大屏管理</h2>
                <p class="text-muted">创建和管理数据可视化大屏</p>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="showCreateModal()">
                    <i class="bi bi-plus-circle"></i> 新建大屏
                </button>
            </div>
        </div>

        <!-- 错误提示 -->
        <div th:if="${error}" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle"></i> <span th:text="${error}"></span>
        </div>

        <!-- 大屏列表 -->
        <div class="row" id="dashboardList">
            <div th:each="dashboard : ${dashboards}" class="col-md-4 col-lg-3 mb-4">
                <div class="card dashboard-card h-100">
                    <div class="dashboard-preview">
                        <i class="bi bi-graph-up" style="font-size: 3rem;"></i>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title" th:text="${dashboard.name}">大屏名称</h5>
                        <p class="card-text text-muted" th:text="${dashboard.description ?: '暂无描述'}">大屏描述</p>
                        <small class="text-muted">
                            创建时间: <span th:text="${dashboard.createdAt != null ? dashboard.createdAt : '未知'}"></span>
                        </small>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex btn-group-actions">
                            <button class="btn btn-outline-primary btn-sm flex-fill"
                                    th:onclick="|designDashboard(${dashboard.id})|">
                                <i class="bi bi-pencil"></i> 设计
                            </button>
                            <button class="btn btn-outline-success btn-sm flex-fill"
                                    th:onclick="|previewDashboard(${dashboard.id})|">
                                <i class="bi bi-eye"></i> 预览
                            </button>
                            <button class="btn btn-outline-info btn-sm flex-fill"
                                    th:onclick="|publishDashboard(${dashboard.id})|"
                                    th:data-name="${dashboard.name}">
                                <i class="bi bi-share"></i> 发布
                            </button>
                            <button class="btn btn-outline-danger btn-sm"
                                    th:onclick="|deleteDashboard(${dashboard.id})|"
                                    th:data-name="${dashboard.name}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 空状态 -->
            <div th:if="${#lists.isEmpty(dashboards)}" class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-bar-chart" style="font-size: 4rem; color: #dee2e6;"></i>
                    <h4 class="mt-3 text-muted">暂无大屏</h4>
                    <p class="text-muted">点击"新建大屏"开始创建您的第一个数据可视化大屏</p>
                    <button class="btn btn-primary" onclick="showCreateModal()">
                        <i class="bi bi-plus-circle"></i> 新建大屏
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建大屏模态框 -->
    <div class="modal fade" id="createDashboardModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新建大屏</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createDashboardForm">
                        <div class="mb-3">
                            <label for="dashboardName" class="form-label">大屏名称 *</label>
                            <input type="text" class="form-control" id="dashboardName" required>
                        </div>
                        <div class="mb-3">
                            <label for="dashboardDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="dashboardDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createDashboard()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示创建模态框
        function showCreateModal() {
            const modal = new bootstrap.Modal(document.getElementById('createDashboardModal'));
            modal.show();
        }

        // 创建大屏
        function createDashboard() {
            const name = document.getElementById('dashboardName').value.trim();
            const description = document.getElementById('dashboardDescription').value.trim();
            
            if (!name) {
                alert('请输入大屏名称');
                return;
            }
            
            const formData = new FormData();
            formData.append('name', name);
            if (description) {
                formData.append('description', description);
            }
            
            fetch('/api/bi/dashboard', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('创建成功');
                    location.reload();
                } else {
                    alert('创建失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('创建失败');
            });
        }

        // 设计大屏
        function designDashboard(id) {
            window.location.href = `/bi/dashboard/${id}/design`;
        }

        // 预览大屏
        function previewDashboard(id) {
            window.open(`/bi/dashboard/${id}/preview`, '_blank');
        }

        // 发布大屏
        function publishDashboard(id) {
            // 从事件源获取名称
            const button = event.target.closest('button');
            const name = button.getAttribute('data-name') || '未知大屏';

            const publishName = prompt(`请输入发布名称:`, `${name} - 发布版`);
            if (!publishName) return;

            const validityDays = prompt(`请选择有效期:\n1 - 1天\n3 - 3天\n7 - 7天\n30 - 30天\n180 - 半年\n0 - 永久`, '7');
            if (validityDays === null) return;

            const formData = new FormData();
            formData.append('dashboardId', id);
            formData.append('name', publishName);
            formData.append('validityDays', validityDays);

            fetch('/bi/published/publish', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`发布成功！\n访问令牌：${data.accessToken}\n\n您可以通过以下链接访问：\n${window.location.origin}/bi/published/${data.accessToken}`);
                } else {
                    alert('发布失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发布失败：网络错误');
            });
        }

        // 删除大屏
        function deleteDashboard(id) {
            // 从事件源获取名称
            const button = event.target.closest('button');
            const name = button.getAttribute('data-name') || '未知大屏';

            // 首先检查是否有发布记录
            fetch(`/api/bi/dashboard/${id}/check-published`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.hasPublished) {
                            // 有发布记录，显示详细确认对话框
                            showDeleteConfirmWithPublished(id, name, data.publishedRecords);
                        } else {
                            // 没有发布记录，直接确认删除
                            if (confirm(`确定要删除大屏"${name}"吗？此操作不可恢复。`)) {
                                performDelete(id);
                            }
                        }
                    } else {
                        alert('检查发布记录失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('检查发布记录失败');
                });
        }

        // 显示有发布记录的删除确认对话框
        function showDeleteConfirmWithPublished(id, name, publishedRecords) {
            let recordsInfo = '';
            publishedRecords.forEach(record => {
                const status = record.status === 'ACTIVE' ? '有效' :
                              record.status === 'EXPIRED' ? '已过期' : '已撤销';
                const publishedDate = new Date(record.publishedAt).toLocaleString();
                recordsInfo += `\n- ${record.name} (${status}, 发布于: ${publishedDate})`;
            });

            const message = `大屏"${name}"存在 ${publishedRecords.length} 条发布记录：${recordsInfo}\n\n删除大屏将同时删除所有发布记录，此操作不可恢复。\n\n确定要继续删除吗？`;

            if (confirm(message)) {
                performForceDelete(id);
            }
        }

        // 执行普通删除
        function performDelete(id) {
            fetch(`/api/bi/dashboard/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('删除成功');
                    location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除失败');
            });
        }

        // 执行强制删除（包含发布记录）
        function performForceDelete(id) {
            fetch(`/api/bi/dashboard/${id}/force-delete`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('删除成功（包含发布记录）');
                    location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除失败');
            });
        }
    </script>
</body>
</html>
