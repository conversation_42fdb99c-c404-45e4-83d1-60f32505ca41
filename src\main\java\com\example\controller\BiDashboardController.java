package com.example.controller;

import com.example.entity.BiDashboard;
import com.example.entity.BiWidget;
import com.example.entity.DataSet;
import com.example.entity.PublishedBiDashboard;
import com.example.service.BiDashboardService;
import com.example.service.BiDataService;
import com.example.service.DataSetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.Optional;

@Controller
@Slf4j
public class BiDashboardController {
    
    @Autowired
    private BiDashboardService dashboardService;

    @Autowired
    private BiDataService dataService;

    @Autowired
    private DataSetService dataSetService;
    
    /**
     * 大屏管理列表页面（简化测试版）
     */
    @GetMapping("/bi/dashboard/test")
    public String dashboardListTest(Model model) {
        try {
            log.info("=== 开始处理简化版大屏列表请求 ===");

            List<BiDashboard> dashboards = dashboardService.getAllDashboards();
            log.info("成功获取到 {} 个大屏对象", dashboards.size());

            model.addAttribute("dashboards", dashboards);
            log.info("=== 简化版大屏列表页面准备完成 ===");
            return "bi/dashboard-list-simple";
        } catch (Exception e) {
            log.error("=== 简化版大屏列表处理失败 ===", e);
            model.addAttribute("error", "获取大屏列表失败: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            model.addAttribute("dashboards", new java.util.ArrayList<>());
            return "bi/dashboard-list-simple";
        }
    }

    /**
     * 大屏管理列表页面
     */
    @GetMapping("/bi/dashboard")
    public String dashboardList(Model model) {
        try {
            log.info("=== 开始处理大屏列表请求 ===");

            List<BiDashboard> dashboards = dashboardService.getAllDashboards();
            log.info("成功获取到 {} 个大屏对象", dashboards.size());

            // 确保widgets字段不会导致序列化问题，并验证数据完整性
            for (BiDashboard dashboard : dashboards) {
                log.info("处理大屏: ID={}, Name={}, CreatedAt={}",
                    dashboard.getId(), dashboard.getName(), dashboard.getCreatedAt());
                dashboard.setWidgets(null);

                // 确保名称不为空，避免模板渲染问题
                if (dashboard.getName() == null) {
                    dashboard.setName("未命名大屏");
                }
                if (dashboard.getDescription() == null) {
                    dashboard.setDescription("");
                }
            }

            model.addAttribute("dashboards", dashboards);
            log.info("=== 大屏列表页面准备完成 ===");
            return "bi/dashboard-list";
        } catch (Exception e) {
            log.error("=== 大屏列表处理失败 ===", e);
            model.addAttribute("error", "获取大屏列表失败: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            model.addAttribute("dashboards", new java.util.ArrayList<>());
            return "bi/dashboard-list";
        }
    }
    
    /**
     * 大屏设计器页面
     */
    @GetMapping("/bi/dashboard/{id}/design")
    public String dashboardDesigner(@PathVariable Long id, Model model) {
        Optional<BiDashboard> dashboardOpt = dashboardService.getDashboardWithWidgets(id);
        if (dashboardOpt.isPresent()) {
            model.addAttribute("dashboard", dashboardOpt.get());
            return "bi/dashboard-designer";
        } else {
            return "redirect:/bi/dashboard?error=notfound";
        }
    }
    
    /**
     * 大屏预览页面
     */
    @GetMapping("/bi/dashboard/{id}/preview")
    public String dashboardPreview(@PathVariable Long id, Model model) {
        Optional<BiDashboard> dashboardOpt = dashboardService.getDashboardWithWidgets(id);
        if (dashboardOpt.isPresent()) {
            model.addAttribute("dashboard", dashboardOpt.get());
            return "bi/dashboard-preview";
        } else {
            return "redirect:/bi/dashboard?error=notfound";
        }
    }
    
    // ==================== API接口 ====================
    
    /**
     * 创建新大屏
     */
    @PostMapping("/api/bi/dashboard")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> createDashboard(
            @RequestParam String name,
            @RequestParam(required = false) String description) {
        try {
            BiDashboard dashboard = dashboardService.createDashboard(name, description);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", dashboard);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("创建大屏失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 保存大屏布局（完善版，参考report项目）
     */
    @PutMapping("/api/bi/dashboard/{id}/layout")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> saveDashboardLayout(
            @PathVariable Long id,
            @RequestBody Map<String, Object> layoutData) {
        try {
            log.info("开始保存大屏布局，ID: {}", id);

            // 使用完善的保存服务
            Map<String, Object> result = dashboardService.saveDashboardLayoutComplete(id, layoutData);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "布局保存成功");
            response.put("savedWidgets", result.get("savedWidgets"));
            response.put("updatedCanvas", result.get("updatedCanvas"));

            log.info("大屏布局保存成功，ID: {}, 保存组件数: {}", id, result.get("savedWidgets"));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("保存布局失败，ID: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "保存失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 保存画布配置
     */
    @PutMapping("/api/dashboards/{id}/canvas-config")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> saveCanvasConfig(
            @PathVariable Long id,
            @RequestBody Map<String, Object> canvasConfig) {
        try {
            // 将配置转换为JSON字符串
            String configJson = new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(canvasConfig);
            dashboardService.saveDashboardLayout(id, configJson);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "画布配置保存成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("保存画布配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 检查大屏发布记录
     */
    @GetMapping("/api/bi/dashboard/{id}/check-published")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> checkPublishedRecords(@PathVariable Long id) {
        try {
            List<PublishedBiDashboard> publishedRecords = dashboardService.checkPublishedRecords(id);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("hasPublished", !publishedRecords.isEmpty());
            response.put("publishedCount", publishedRecords.size());

            if (!publishedRecords.isEmpty()) {
                List<Map<String, Object>> recordList = new ArrayList<>();
                for (PublishedBiDashboard record : publishedRecords) {
                    Map<String, Object> recordInfo = new HashMap<>();
                    recordInfo.put("id", record.getId());
                    recordInfo.put("name", record.getName());
                    recordInfo.put("status", record.getStatus());
                    recordInfo.put("publishedAt", record.getPublishedAt());
                    recordInfo.put("expiryDate", record.getExpiryDate());
                    recordList.add(recordInfo);
                }
                response.put("publishedRecords", recordList);
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("检查发布记录失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 删除大屏（检查发布记录约束）
     */
    @DeleteMapping("/api/bi/dashboard/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteDashboard(@PathVariable Long id) {
        try {
            dashboardService.deleteDashboard(id);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除大屏失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 强制删除大屏（包含发布记录）
     */
    @DeleteMapping("/api/bi/dashboard/{id}/force-delete")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> forceDeleteDashboard(@PathVariable Long id) {
        try {
            dashboardService.deleteDashboardWithPublished(id);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "删除成功（包含发布记录）");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("强制删除大屏失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 添加组件
     */
    @PostMapping("/api/bi/dashboard/{dashboardId}/widget")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> addWidget(
            @PathVariable Long dashboardId,
            @RequestParam String widgetType,
            @RequestParam Integer x,
            @RequestParam Integer y,
            @RequestParam Integer width,
            @RequestParam Integer height) {
        try {
            BiWidget widget = dashboardService.addWidget(dashboardId, widgetType, x, y, width, height);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", widget);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("添加组件失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 更新组件
     */
    @PutMapping("/api/bi/widget/{widgetId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> updateWidget(
            @PathVariable Long widgetId,
            @RequestParam(required = false) String config,
            @RequestParam(required = false) String dataSourceConfig) {
        try {
            dashboardService.updateWidget(widgetId, config, dataSourceConfig);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "更新成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新组件失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 删除组件
     */
    @DeleteMapping("/api/bi/widget/{widgetId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteWidget(@PathVariable Long widgetId) {
        try {
            dashboardService.deleteWidget(widgetId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除组件失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取大屏组件列表
     */
    @GetMapping("/api/bi/dashboard/{dashboardId}/widgets")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDashboardWidgets(@PathVariable Long dashboardId) {
        try {
            List<BiWidget> widgets = dashboardService.getDashboardWidgets(dashboardId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", widgets);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取组件列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    // ==================== 数据API接口 ====================

    /**
     * 获取设备列表（用于数据源选择）
     */
    @GetMapping("/api/bi/devices")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDevices() {
        try {
            List<Map<String, Object>> devices = dataService.getAllDevicesForSelect();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", devices);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取设备数据项
     */
    @GetMapping("/api/bi/devices/{deviceId}/items")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDeviceDataItems(@PathVariable String deviceId) {
        try {
            List<Map<String, Object>> items = dataService.getDeviceDataItems(deviceId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", items);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取设备数据项失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取组件数据
     */
    @PostMapping("/api/bi/data/widget")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getWidgetData(@RequestBody Map<String, Object> request) {
        try {
            String widgetType = (String) request.get("widgetType");
            Map<String, Object> dataSourceConfig = new HashMap<>(request);
            dataSourceConfig.remove("widgetType"); // 移除widgetType，只保留数据源配置

            Map<String, Object> data = dataService.getWidgetData(widgetType, dataSourceConfig);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            log.error("获取组件数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取监控项实时数据
     */
    @GetMapping("/api/bi/data/realtime/{dataItemId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDataItemRealTime(@PathVariable String dataItemId) {
        try {
            Map<String, Object> data = dataService.getDataItemRealTimeValue(dataItemId);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            log.error("获取监控项实时数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取监控项历史数据（按时间范围）
     */
    @GetMapping("/api/bi/data/history/{dataItemId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDataItemHistory(
            @PathVariable String dataItemId,
            @RequestParam(defaultValue = "24") int hours) {
        try {
            Map<String, Object> data = dataService.getDataItemHistoryData(dataItemId, hours);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            log.error("获取监控项历史数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取监控项最新N条历史数据
     */
    @GetMapping("/api/bi/data/latest/{dataItemId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDataItemLatestHistory(
            @PathVariable String dataItemId,
            @RequestParam(defaultValue = "50") int count) {
        try {
            Map<String, Object> data = dataService.getDataItemLatestHistoryData(dataItemId, count);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            log.error("获取监控项最新历史数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取设备统计信息
     */
    @GetMapping("/api/bi/statistics")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = dataService.getDeviceStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    // ==================== 外部数据源API接口 ====================

    /**
     * 获取数据集列表（用于BI组件数据源选择）
     */
    @GetMapping("/api/bi/datasets")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDataSetsForBI() {
        try {
            List<DataSet> dataSets = dataSetService.getAllDataSets();

            // 转换为BI组件需要的格式
            List<Map<String, Object>> dataSetList = dataSets.stream().map(dataSet -> {
                Map<String, Object> item = new HashMap<>();
                item.put("id", dataSet.getId());
                item.put("name", dataSet.getName());
                item.put("description", dataSet.getDescription());
                item.put("dataSourceName", dataSet.getDataSourceName());
                item.put("createdAt", dataSet.getCreatedAt());
                return item;
            }).collect(java.util.stream.Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", dataSetList);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取数据集列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取数据集字段信息（用于BI组件字段选择）
     */
    @GetMapping("/api/bi/dataset/{id}/fields")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDataSetFieldsForBI(@PathVariable String id) {
        try {
            // 执行数据集查询获取字段信息
            com.example.service.connector.DataSourceConnector.QueryResult queryResult = dataSetService.executeDataSet(id);

            if (!queryResult.isSuccess()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", queryResult.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

            List<Map<String, Object>> rawData = queryResult.getData();

            // 分析字段信息
            List<Map<String, Object>> fields = analyzeDataSetFields(rawData);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("fields", fields);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取数据集字段信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取数据集数据（用于BI组件显示）
     */
    @GetMapping("/api/bi/dataset/{id}/data")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDataSetDataForBI(
            @PathVariable String id,
            @RequestParam(required = false) String labelField,
            @RequestParam(required = false) String valueField) {
        try {
            com.example.service.connector.DataSourceConnector.QueryResult queryResult = dataSetService.executeDataSet(id);

            if (!queryResult.isSuccess()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", queryResult.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

            List<Map<String, Object>> rawData = queryResult.getData();

            // 转换数据格式以适配图表组件（支持字段选择）
            Map<String, Object> chartData = convertDataSetToChartFormat(rawData, labelField, valueField);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", chartData);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取数据集数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 分析数据集字段信息
     */
    private List<Map<String, Object>> analyzeDataSetFields(List<Map<String, Object>> rawData) {
        List<Map<String, Object>> fields = new ArrayList<>();

        if (rawData == null || rawData.isEmpty()) {
            return fields;
        }

        // 获取第一行数据来分析字段
        Map<String, Object> firstRow = rawData.get(0);

        for (String fieldName : firstRow.keySet()) {
            Map<String, Object> field = new HashMap<>();
            field.put("name", fieldName);
            field.put("displayName", fieldName);

            // 分析字段类型和示例值
            Object sampleValue = firstRow.get(fieldName);
            String fieldType = "text";
            String sampleText = "";

            if (sampleValue != null) {
                sampleText = sampleValue.toString();

                // 尝试判断是否为数值类型
                try {
                    Double.parseDouble(sampleText);
                    fieldType = "number";
                } catch (NumberFormatException e) {
                    // 检查是否为日期类型
                    if (sampleText.matches("\\d{4}-\\d{2}-\\d{2}.*") ||
                        sampleText.matches("\\d{2}/\\d{2}/\\d{4}.*")) {
                        fieldType = "date";
                    } else {
                        fieldType = "text";
                    }
                }
            }

            field.put("type", fieldType);
            field.put("sampleValue", sampleText);

            // 建议用途
            if ("number".equals(fieldType)) {
                field.put("suggestedFor", "value");
                field.put("description", "数值字段，适合作为Y轴数据");
            } else if ("date".equals(fieldType)) {
                field.put("suggestedFor", "label");
                field.put("description", "日期字段，适合作为X轴标签");
            } else {
                field.put("suggestedFor", "label");
                field.put("description", "文本字段，适合作为X轴标签");
            }

            fields.add(field);
        }

        return fields;
    }

    /**
     * 转换数据集数据为图表格式（支持字段选择）
     */
    private Map<String, Object> convertDataSetToChartFormat(List<Map<String, Object>> rawData, String labelField, String valueField) {
        Map<String, Object> result = new HashMap<>();

        if (rawData == null || rawData.isEmpty()) {
            result.put("labels", new ArrayList<>());
            result.put("values", new ArrayList<>());
            return result;
        }

        // 如果指定了字段，使用指定的字段
        if (labelField != null && valueField != null && !labelField.isEmpty() && !valueField.isEmpty()) {
            return convertWithSpecifiedFields(rawData, labelField, valueField);
        }

        // 否则使用原有的自动选择逻辑
        return convertDataSetToChartFormat(rawData);
    }

    /**
     * 使用指定字段转换数据
     */
    private Map<String, Object> convertWithSpecifiedFields(List<Map<String, Object>> rawData, String labelField, String valueField) {
        Map<String, Object> result = new HashMap<>();
        List<String> labels = new ArrayList<>();
        List<Number> values = new ArrayList<>();

        for (Map<String, Object> row : rawData) {
            Object labelObj = row.get(labelField);
            Object valueObj = row.get(valueField);

            String label = labelObj != null ? labelObj.toString() : "";
            Number value = 0;

            if (valueObj instanceof Number) {
                value = (Number) valueObj;
            } else if (valueObj != null) {
                try {
                    value = Double.parseDouble(valueObj.toString());
                } catch (NumberFormatException e) {
                    value = 0;
                }
            }

            labels.add(label);
            values.add(value);
        }

        result.put("labels", labels);
        result.put("values", values);
        return result;
    }

    /**
     * 转换数据集数据为图表格式（原有逻辑）
     */
    private Map<String, Object> convertDataSetToChartFormat(List<Map<String, Object>> rawData) {
        Map<String, Object> result = new HashMap<>();

        if (rawData == null || rawData.isEmpty()) {
            result.put("labels", new ArrayList<>());
            result.put("values", new ArrayList<>());
            return result;
        }

        // 假设第一行是标题行或者数据行
        Map<String, Object> firstRow = rawData.get(0);
        java.util.Set<String> keys = firstRow.keySet();

        if (keys.size() >= 2) {
            // 如果有至少两列，第一列作为标签，第二列作为数值
            String labelKey = keys.iterator().next();
            String valueKey = keys.stream().skip(1).findFirst().orElse(labelKey);

            List<String> labels = new ArrayList<>();
            List<Number> values = new ArrayList<>();

            for (Map<String, Object> row : rawData) {
                Object labelObj = row.get(labelKey);
                Object valueObj = row.get(valueKey);

                String label = labelObj != null ? labelObj.toString() : "";
                Number value = 0;

                if (valueObj instanceof Number) {
                    value = (Number) valueObj;
                } else if (valueObj != null) {
                    try {
                        value = Double.parseDouble(valueObj.toString());
                    } catch (NumberFormatException e) {
                        value = 0;
                    }
                }

                labels.add(label);
                values.add(value);
            }

            result.put("labels", labels);
            result.put("values", values);
        } else {
            // 如果只有一列，使用行号作为标签
            List<String> labels = new ArrayList<>();
            List<Number> values = new ArrayList<>();

            for (int i = 0; i < rawData.size(); i++) {
                Map<String, Object> row = rawData.get(i);
                labels.add("项目" + (i + 1));

                Object valueObj = row.values().iterator().next();
                Number value = 0;

                if (valueObj instanceof Number) {
                    value = (Number) valueObj;
                } else if (valueObj != null) {
                    try {
                        value = Double.parseDouble(valueObj.toString());
                    } catch (NumberFormatException e) {
                        value = 0;
                    }
                }

                values.add(value);
            }

            result.put("labels", labels);
            result.put("values", values);
        }

        return result;
    }

    /**
     * 批量获取数据集数据（用于多数据集功能）
     */
    @PostMapping("/api/bi/datasets/batch")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getBatchDataSetsData(@RequestBody BatchDataSetRequest request) {
        try {
            List<BatchDataSetRequest.DataSetConfig> dataSets = request.getDataSets();
            String mergeStrategy = request.getMergeStrategy();
            String componentType = request.getComponentType();

            if (dataSets == null || dataSets.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "未提供数据集配置");
                return ResponseEntity.badRequest().body(response);
            }

            log.info("批量获取数据集数据，数据集数量: {}, 合并策略: {}, 组件类型: {}",
                    dataSets.size(), mergeStrategy, componentType);

            // 并行查询所有数据集
            List<CompletableFuture<BatchDataSetResult>> futures = dataSets.stream()
                .map(dataSetConfig -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return queryDataSetForBatch(dataSetConfig, componentType);
                    } catch (Exception e) {
                        log.error("查询数据集失败: {}", dataSetConfig.getDataSetId(), e);
                        return new BatchDataSetResult(dataSetConfig.getAlias(), false, e.getMessage(), null);
                    }
                }))
                .collect(Collectors.toList());

            // 等待所有查询完成
            List<BatchDataSetResult> results = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

            // 合并数据
            Map<String, Object> mergedData = mergeDataSetResults(results, mergeStrategy, componentType);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", mergedData);
            response.put("message", String.format("成功处理 %d 个数据集", results.size()));

            // 添加警告信息（如果有失败的数据集）
            List<String> warnings = results.stream()
                .filter(r -> !r.isSuccess())
                .map(r -> r.getAlias() + ": " + r.getError())
                .collect(Collectors.toList());

            if (!warnings.isEmpty()) {
                response.put("warnings", warnings);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("批量获取数据集数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 查询单个数据集用于批量处理
     */
    private BatchDataSetResult queryDataSetForBatch(BatchDataSetRequest.DataSetConfig dataSetConfig, String componentType) {
        try {
            String dataSetId = dataSetConfig.getDataSetId();
            String alias = dataSetConfig.getAlias();

            // 执行数据集查询
            com.example.service.connector.DataSourceConnector.QueryResult queryResult = dataSetService.executeDataSet(dataSetId);

            if (!queryResult.isSuccess()) {
                return new BatchDataSetResult(alias, false, queryResult.getMessage(), null);
            }

            List<Map<String, Object>> rawData = queryResult.getData();
            if (rawData == null || rawData.isEmpty()) {
                return new BatchDataSetResult(alias, true, "数据集为空", new HashMap<>());
            }

            // 根据组件类型格式化数据
            Map<String, Object> formattedData;
            if ("data-table".equals(componentType)) {
                formattedData = formatDataSetForTable(rawData, dataSetConfig.getTableFields());
            } else {
                formattedData = convertDataSetToChartFormat(rawData,
                    dataSetConfig.getLabelField(), dataSetConfig.getValueField());
            }

            return new BatchDataSetResult(alias, true, "成功", formattedData);

        } catch (Exception e) {
            log.error("查询数据集失败: {}", dataSetConfig.getDataSetId(), e);
            return new BatchDataSetResult(dataSetConfig.getAlias(), false, e.getMessage(), null);
        }
    }

    /**
     * 格式化数据集为表格格式
     */
    private Map<String, Object> formatDataSetForTable(List<Map<String, Object>> rawData,
            List<Map<String, Object>> tableFields) {

        if (tableFields == null || tableFields.isEmpty()) {
            // 如果没有字段配置，返回原始数据
            Map<String, Object> result = new HashMap<>();
            result.put("data", rawData);
            return result;
        }

        List<Map<String, Object>> tableData = new ArrayList<>();
        for (Map<String, Object> rawRow : rawData) {
            Map<String, Object> tableRow = new HashMap<>();

            for (Map<String, Object> fieldConfig : tableFields) {
                String displayName = (String) fieldConfig.get("displayName");
                String dataField = (String) fieldConfig.get("dataField");

                if (displayName != null && dataField != null) {
                    Object value = rawRow.get(dataField);
                    tableRow.put(displayName, value != null ? value : "");
                }
            }

            tableData.add(tableRow);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("data", tableData);
        return result;
    }

    /**
     * 合并数据集查询结果
     */
    private Map<String, Object> mergeDataSetResults(List<BatchDataSetResult> results,
            String mergeStrategy, String componentType) {

        // 过滤成功的结果
        List<BatchDataSetResult> successResults = results.stream()
            .filter(BatchDataSetResult::isSuccess)
            .collect(Collectors.toList());

        if (successResults.isEmpty()) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("labels", Arrays.asList("错误"));
            errorResult.put("values", Arrays.asList(0));

            Map<String, Object> errorDataItem = new HashMap<>();
            errorDataItem.put("name", "错误");
            errorDataItem.put("value", 0);
            errorResult.put("data", Arrays.asList(errorDataItem));
            return errorResult;
        }

        if ("data-table".equals(componentType)) {
            return mergeTableResults(successResults, mergeStrategy);
        } else {
            return mergeChartResults(successResults, mergeStrategy, componentType);
        }
    }

    /**
     * 合并表格结果
     */
    private Map<String, Object> mergeTableResults(List<BatchDataSetResult> results, String mergeStrategy) {
        List<Map<String, Object>> allData = new ArrayList<>();
        Set<String> allColumns = new LinkedHashSet<>();

        for (int i = 0; i < results.size(); i++) {
            BatchDataSetResult result = results.get(i);
            Map<String, Object> data = result.getData();

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> tableData = (List<Map<String, Object>>) data.get("data");

            if (tableData != null) {
                // 收集所有列名
                tableData.forEach(row -> allColumns.addAll(row.keySet()));

                if ("separate".equals(mergeStrategy) && i > 0) {
                    // 分离模式：添加分隔行
                    Map<String, Object> separatorRow = new HashMap<>();
                    allColumns.forEach(col -> separatorRow.put(col, "---"));
                    allData.add(separatorRow);

                    // 添加数据集标题行
                    Map<String, Object> titleRow = new HashMap<>();
                    boolean first = true;
                    for (String col : allColumns) {
                        titleRow.put(col, first ? result.getAlias() : "");
                        first = false;
                    }
                    allData.add(titleRow);
                } else if ("union".equals(mergeStrategy)) {
                    // 联合模式：为每行添加数据源标识
                    tableData.forEach(row -> {
                        row.put("数据源", result.getAlias());
                        allColumns.add("数据源");
                    });
                }

                allData.addAll(tableData);
            }
        }

        Map<String, Object> mergedResult = new HashMap<>();
        mergedResult.put("data", allData);
        mergedResult.put("columns", new ArrayList<>(allColumns));
        return mergedResult;
    }

    /**
     * 合并图表结果
     */
    private Map<String, Object> mergeChartResults(List<BatchDataSetResult> results,
            String mergeStrategy, String componentType) {

        List<String> mergedLabels = new ArrayList<>();
        List<Object> mergedValues = new ArrayList<>();
        List<Map<String, Object>> mergedData = new ArrayList<>();

        for (int i = 0; i < results.size(); i++) {
            BatchDataSetResult result = results.get(i);
            Map<String, Object> data = result.getData();

            @SuppressWarnings("unchecked")
            List<String> labels = (List<String>) data.get("labels");
            @SuppressWarnings("unchecked")
            List<Object> values = (List<Object>) data.get("values");

            if (labels != null && values != null) {
                if ("separate".equals(mergeStrategy) && i > 0) {
                    // 分离模式：添加分隔标识
                    mergedLabels.add("---");
                    mergedValues.add(0);
                    mergedLabels.add(result.getAlias());
                    mergedValues.add(0);
                }

                for (int j = 0; j < labels.size() && j < values.size(); j++) {
                    String label = labels.get(j);
                    Object value = values.get(j);

                    if ("union".equals(mergeStrategy)) {
                        label = label + " (" + result.getAlias() + ")";
                    }

                    mergedLabels.add(label);
                    mergedValues.add(value);

                    if ("pie-chart".equals(componentType)) {
                        Map<String, Object> pieItem = new HashMap<>();
                        pieItem.put("name", label);
                        pieItem.put("value", value);
                        mergedData.add(pieItem);
                    }
                }
            }
        }

        Map<String, Object> mergedResult = new HashMap<>();
        mergedResult.put("labels", mergedLabels);
        mergedResult.put("values", mergedValues);

        if ("pie-chart".equals(componentType)) {
            mergedResult.put("data", mergedData);
        }

        return mergedResult;
    }

    /**
     * 批量数据集查询请求类
     */
    public static class BatchDataSetRequest {
        private List<DataSetConfig> dataSets;
        private String mergeStrategy;
        private String componentType;

        // Getters and Setters
        public List<DataSetConfig> getDataSets() { return dataSets; }
        public void setDataSets(List<DataSetConfig> dataSets) { this.dataSets = dataSets; }
        public String getMergeStrategy() { return mergeStrategy; }
        public void setMergeStrategy(String mergeStrategy) { this.mergeStrategy = mergeStrategy; }
        public String getComponentType() { return componentType; }
        public void setComponentType(String componentType) { this.componentType = componentType; }

        public static class DataSetConfig {
            private String dataSetId;
            private String alias;
            private String labelField;
            private String valueField;
            private List<Map<String, Object>> tableFields;

            // Getters and Setters
            public String getDataSetId() { return dataSetId; }
            public void setDataSetId(String dataSetId) { this.dataSetId = dataSetId; }
            public String getAlias() { return alias; }
            public void setAlias(String alias) { this.alias = alias; }
            public String getLabelField() { return labelField; }
            public void setLabelField(String labelField) { this.labelField = labelField; }
            public String getValueField() { return valueField; }
            public void setValueField(String valueField) { this.valueField = valueField; }
            public List<Map<String, Object>> getTableFields() { return tableFields; }
            public void setTableFields(List<Map<String, Object>> tableFields) { this.tableFields = tableFields; }
        }
    }

    /**
     * 批量数据集查询结果类
     */
    public static class BatchDataSetResult {
        private String alias;
        private boolean success;
        private String error;
        private Map<String, Object> data;

        public BatchDataSetResult(String alias, boolean success, String error, Map<String, Object> data) {
            this.alias = alias;
            this.success = success;
            this.error = error;
            this.data = data;
        }

        // Getters
        public String getAlias() { return alias; }
        public boolean isSuccess() { return success; }
        public String getError() { return error; }
        public Map<String, Object> getData() { return data; }
    }
}
