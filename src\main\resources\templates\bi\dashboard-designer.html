<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'设计大屏 - ' + ${dashboard.name} + ' - 胜大科技智联管理系统'">设计大屏</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/bi-dashboard.css" rel="stylesheet">
    <link href="/css/button-styles.css?v=20250731-icon-fix" rel="stylesheet">
    <link href="/css/bi-status-indicator.css?v=20250730-fixed-shapes" rel="stylesheet">
    <style>
        body { margin: 0; padding: 0; overflow: hidden; }
        .designer-container { height: 100vh; display: flex; flex-direction: column; }
        .designer-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1rem;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* 设计器导航条按钮样式 - 与标准navbar保持一致 */
        .designer-header .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            transition: all 0.3s ease;
        }

        .designer-header .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: white;
            transform: translateY(-1px);
        }
        .designer-body { flex: 1; display: flex; overflow: hidden; }
        .component-panel { width: 250px; background: #f8f9fa; border-right: 1px solid #dee2e6; overflow-y: auto; }
        .canvas-container { flex: 1; position: relative; overflow: auto; background: #e9ecef; }
        .property-panel { width: 300px; background: #f8f9fa; border-left: 1px solid #dee2e6; overflow-y: auto; }
        .canvas-wrapper {
            position: relative;
            display: inline-block;
            margin: 20px;
        }
        .canvas {
            position: relative; background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15); border: 1px solid #dee2e6;
            transform-origin: top left;
        }
        .grid-background {
            background-image: 
                linear-gradient(to right, #f0f0f0 1px, transparent 1px),
                linear-gradient(to bottom, #f0f0f0 1px, transparent 1px);
            background-size: 20px 20px;
        }
        .component-item {
            padding: 0.75rem; margin: 0.5rem; background: white; border: 1px solid #dee2e6;
            border-radius: 0.375rem; cursor: grab; transition: all 0.2s;
        }
        .component-item:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .widget {
            position: absolute; border: 2px solid transparent; background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1); cursor: move; min-width: 20px; min-height: 20px;
        }
        .widget.selected { border-color: #0d6efd; }
        .widget.hover { border-color: #6c757d; }
        .widget-header { 
            background: #f8f9fa; padding: 0.25rem 0.5rem; font-size: 0.875rem; 
            border-bottom: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center;
        }
        .widget-content { padding: 0.5rem; height: calc(100% - 32px); }
        .resize-handle {
            position: absolute;
            bottom: -8px;
            right: -8px;
            width: 16px;
            height: 16px;
            cursor: se-resize;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .resize-dot {
            width: 10px;
            height: 10px;
            background: #007bff;
            border: 2px solid #fff;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
        }

        .widget:hover .resize-dot {
            background: #0056b3;
            transform: scale(1.2);
        }

        .widget.selected .resize-dot {
            background: #28a745;
        }

        .widget.resizing {
            opacity: 0.8;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .widget.resizing .resize-dot {
            background: #ffc107;
            transform: scale(1.3);
        }

        /* 对齐辅助线样式 */
        .alignment-guide {
            position: absolute;
            background: #007bff;
            z-index: 999;
            pointer-events: none;
        }
        .alignment-guide.horizontal {
            height: 1px;
            width: 100%;
            left: 0;
        }
        .alignment-guide.vertical {
            width: 1px;
            height: 100%;
            top: 0;
        }
        .alignment-guide.center-horizontal {
            background: #28a745;
        }
        .alignment-guide.center-vertical {
            background: #28a745;
        }

        /* 图层管理样式 */
        .layers-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .layer-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            cursor: grab;
            transition: all 0.2s;
        }
        .layer-item:hover {
            background: #f8f9fa;
            border-color: #007bff;
        }
        .layer-item.selected {
            background: #e3f2fd;
            border-color: #007bff;
        }
        .layer-item.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }
        .layer-item .layer-icon {
            width: 24px;
            height: 24px;
            margin-right: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 0.25rem;
            color: #6c757d;
        }
        .layer-item .layer-info {
            flex: 1;
        }
        .layer-item .layer-name {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.25rem;
        }
        .layer-item .layer-type {
            font-size: 0.75rem;
            color: #6c757d;
        }
        .layer-item .layer-actions {
            display: flex;
            gap: 0.25rem;
        }
        .layer-drop-indicator {
            height: 2px;
            background: #007bff;
            margin: 0.25rem 0;
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.2s;
        }
        .layer-drop-indicator.active {
            opacity: 1;
        }

        .property-section { padding: 1rem; border-bottom: 1px solid #dee2e6; }
        .property-section h6 { margin-bottom: 0.75rem; color: #495057; }

        /* 标签页样式 */
        .nav-tabs-sm .nav-link {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
        .nav-tabs-sm .nav-link i {
            margin-right: 0.25rem;
        }
        .tab-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        /* 保存提示动画 */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* 超链接按钮样式 */
        .hyperlink-button {
            transition: all 0.3s ease !important;
        }
        .hyperlink-button:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
            filter: brightness(1.1) !important;
        }
        .hyperlink-button:active {
            transform: translateY(0) !important;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1) !important;
        }

        /* 多外部数据集样式 */
        .multi-external-dataset-container {
            display: none;
        }
        .multi-external-dataset-item {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }
        .multi-external-dataset-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.1);
        }
        .multi-external-dataset-item .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-weight: 500;
        }
        .multi-external-dataset-item .card-body {
            background: white;
        }
        .dataset-alias-input {
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        .dataset-alias-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        .table-fields-container {
            border: 1px solid #e9ecef;
            border-radius: 0.25rem;
            padding: 0.75rem;
            background: #f8f9fa;
        }
        .table-fields-list .card {
            border: 1px solid #dee2e6;
            margin-bottom: 0.5rem;
        }
        .table-fields-list .card:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="designer-container">
        <!-- 设计器头部 -->
        <div class="designer-header">
            <div class="d-flex align-items-center">
                <a href="/bi/dashboard" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>

                <!-- 快速导航 -->
                <a href="/" class="btn btn-outline-light btn-sm ms-3" title="主页">
                    <i class="bi bi-house-door"></i> 主页
                </a>
                <a href="/device/management" class="btn btn-outline-light btn-sm ms-3" title="设备管理">
                    <i class="bi bi-cpu"></i> 设备管理
                </a>
                <a href="/topology" class="btn btn-outline-light btn-sm ms-3" title="组态">
                    <i class="bi bi-diagram-3"></i> 组态
                </a>
                <a href="/file-manager" class="btn btn-outline-light btn-sm ms-3" title="文件管理">
                    <i class="bi bi-images"></i> 文件管理
                </a>
                <a href="/datasource" class="btn btn-outline-light btn-sm ms-3" title="数据源管理">
                    <i class="bi bi-database"></i> 数据源管理
                </a>

                <h5 class="mb-0 ms-4" th:text="${dashboard.name}">大屏名称</h5>
            </div>
            <div class="d-flex gap-2">

                <!-- 缩放控制 -->
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-light btn-sm" onclick="zoomOut()" title="缩小 (Ctrl+-)">
                        <i class="bi bi-dash"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm" id="zoomLevel" onclick="showZoomOptions()" title="缩放级别">
                        100%
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm" onclick="zoomIn()" title="放大 (Ctrl++)">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm" onclick="zoomToFit()" title="适应窗口">
                        <i class="bi bi-arrows-fullscreen"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm" id="alignModeBtn" onclick="toggleAlignMode()" title="切换对齐方式">
                        <i class="bi bi-align-start"></i>
                    </button>
                </div>

                <!-- 对齐设置 -->
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-light btn-sm active" id="gridAlignBtn" onclick="toggleGridAlign()" title="网格对齐">
                        <i class="bi bi-grid-3x3"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm active" id="smartAlignBtn" onclick="toggleSmartAlign()" title="智能对齐">
                        <i class="bi bi-magnet"></i>
                    </button>
                </div>

                <div class="vr"></div>

                <button class="btn btn-outline-light btn-sm" onclick="saveDashboard()">
                    <i class="bi bi-save"></i> 保存
                </button>
                <button class="btn btn-outline-light btn-sm" onclick="previewDashboard()">
                    <i class="bi bi-eye"></i> 预览
                </button>
                <button class="btn btn-outline-light btn-sm" onclick="clearCanvas()">
                    <i class="bi bi-trash"></i> 清空
                </button>
            </div>
        </div>

        <!-- 设计器主体 -->
        <div class="designer-body">
            <!-- 组件面板 -->
            <div class="component-panel">
                <div class="p-3">
                    <h6><i class="bi bi-puzzle"></i> 组件库</h6>
                </div>
                
                <div class="component-category">
                    <div class="px-3 py-2 bg-light">
                        <small class="text-muted fw-bold">图表组件</small>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="line-chart">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-graph-up text-primary me-2"></i>
                            <div>
                                <div class="fw-bold">折线图</div>
                                <small class="text-muted">时序数据展示 · 丰富样式配置</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="multi-line-chart">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-graph-up-arrow text-primary me-2"></i>
                            <div>
                                <div class="fw-bold">多折线图</div>
                                <small class="text-muted">多系列对比 · 双Y轴支持</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="bar-chart">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-bar-chart text-success me-2"></i>
                            <div>
                                <div class="fw-bold">柱状图</div>
                                <small class="text-muted">分类数据对比 · 渐变色彩</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="horizontal-bar-chart">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-bar-chart-horizontal text-success me-2"></i>
                            <div>
                                <div class="fw-bold">水平柱状图</div>
                                <small class="text-muted">水平数据对比 · 渐变色彩</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="gauge-chart">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-speedometer text-warning me-2"></i>
                            <div>
                                <div class="fw-bold">仪表盘</div>
                                <small class="text-muted">单值数据展示 · 多色段显示</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="pie-chart">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-pie-chart text-danger me-2"></i>
                            <div>
                                <div class="fw-bold">饼图</div>
                                <small class="text-muted">占比数据展示 · 3D效果</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="water-chart">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-droplet-fill text-info me-2"></i>
                            <div>
                                <div class="fw-bold">水波图</div>
                                <small class="text-muted">进度展示 · 动态水波</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="column-percentage-chart">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-thermometer-half text-success me-2"></i>
                            <div>
                                <div class="fw-bold">柱状百分比图</div>
                                <small class="text-muted">温度计样式 · 时尚渐变</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="data-table">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-table text-info me-2"></i>
                            <div>
                                <div class="fw-bold">数据表格</div>
                                <small class="text-muted">列表数据展示 · 实时更新</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="component-category">
                    <div class="px-3 py-2 bg-light">
                        <small class="text-muted fw-bold">基础组件</small>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="text-label">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-type text-secondary me-2"></i>
                            <div>
                                <div class="fw-bold">文本标签</div>
                                <small class="text-muted">静态文本显示</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="image-widget">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-image text-primary me-2"></i>
                            <div>
                                <div class="fw-bold">图片组件</div>
                                <small class="text-muted">图片展示</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="hyperlink-widget">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-link-45deg text-primary me-2"></i>
                            <div>
                                <div class="fw-bold">超链接组件</div>
                                <small class="text-muted">超链接展示</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="status-indicator">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-circle-fill text-success me-2"></i>
                            <div>
                                <div class="fw-bold">状态指示器</div>
                                <small class="text-muted">多形状状态监控 · 实时数据</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="decoration-widget">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-stars text-warning me-2"></i>
                            <div>
                                <div class="fw-bold">装饰组件</div>
                                <small class="text-muted">动态装饰效果</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="html-widget">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-code-square text-info me-2"></i>
                            <div>
                                <div class="fw-bold">HTML组件</div>
                                <small class="text-muted">自定义HTML效果</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="video-widget">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-play-circle text-danger me-2"></i>
                            <div>
                                <div class="fw-bold">视频组件</div>
                                <small class="text-muted">URL视频播放</small>
                            </div>
                        </div>
                    </div>
                    <div class="component-item" draggable="true" data-widget-type="time-widget">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-clock text-success me-2"></i>
                            <div>
                                <div class="fw-bold">时间组件</div>
                                <small class="text-muted">实时时间显示</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 画布容器 -->
            <div class="canvas-container" id="canvasContainer">
                <div class="canvas-wrapper" id="canvasWrapper">
                    <div class="canvas" id="canvas"
                         style="width: 1920px; height: 1080px;"
                         ondrop="dropWidget(event)"
                         ondragover="allowDrop(event)"
                         onclick="clearSelection()">
                        <!-- 组件将动态添加到这里 -->
                    </div>
                </div>
            </div>

            <!-- 属性面板 -->
            <div class="property-panel" id="propertyPanel">
                <div class="p-3">
                    <h6><i class="bi bi-sliders"></i> 属性配置</h6>
                </div>
                
                <div id="noSelection" class="text-center p-4 text-muted">
                    <i class="bi bi-cursor" style="font-size: 2rem;"></i>
                    <p class="mt-2">请选择一个组件</p>
                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="showCanvasProperties()">
                        <i class="bi bi-palette"></i> 配置画布背景
                    </button>
                </div>

                <!-- 画布背景配置面板 -->
                <div id="canvasProperties" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0"><i class="bi bi-palette"></i> 画布背景配置</h6>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="hideCanvasProperties()">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>

                    <!-- 画布尺寸配置 -->
                    <div class="property-section">
                        <h6>画布尺寸</h6>
                        <div class="row mb-3">
                            <div class="col-6">
                                <label class="form-label">宽度 (px)</label>
                                <input type="number" class="form-control form-control-sm" id="canvasWidth" min="800" max="4000" step="10">
                            </div>
                            <div class="col-6">
                                <label class="form-label">高度 (px)</label>
                                <input type="number" class="form-control form-control-sm" id="canvasHeight" min="600" max="3000" step="10">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">常用尺寸</label>
                            <select class="form-select form-select-sm" id="canvasPresetSize" onchange="applyPresetSize()">
                                <option value="">自定义尺寸</option>
                                <option value="1920x1080">1920×1080 (Full HD)</option>
                                <option value="1366x768">1366×768 (HD)</option>
                                <option value="1280x720">1280×720 (HD Ready)</option>
                                <option value="1600x900">1600×900 (HD+)</option>
                                <option value="2560x1440">2560×1440 (2K)</option>
                                <option value="3840x2160">3840×2160 (4K)</option>
                            </select>
                        </div>
                    </div>

                    <!-- 背景配置 -->
                    <div class="property-section">
                        <h6>背景配置</h6>
                        <div class="mb-3">
                            <label class="form-label">背景类型</label>
                            <select class="form-select form-select-sm" id="backgroundType" onchange="onBackgroundTypeChange()">
                                <option value="color">纯色背景</option>
                                <option value="gradient">渐变背景</option>
                                <option value="image">图片背景</option>
                            </select>
                        </div>

                        <!-- 纯色背景配置 -->
                        <div id="colorBackgroundConfig">
                            <div class="mb-3">
                                <label class="form-label">背景颜色</label>
                                <input type="color" class="form-control form-control-color form-control-sm" id="canvasBackgroundColor" value="#ffffff">
                            </div>
                        </div>

                        <!-- 渐变背景配置 -->
                        <div id="gradientBackgroundConfig" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">渐变方向</label>
                                <select class="form-select form-select-sm" id="gradientDirection">
                                    <option value="to bottom">从上到下</option>
                                    <option value="to top">从下到上</option>
                                    <option value="to right">从左到右</option>
                                    <option value="to left">从右到左</option>
                                    <option value="45deg">对角线(左上到右下)</option>
                                    <option value="135deg">对角线(右上到左下)</option>
                                </select>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6">
                                    <label class="form-label">起始颜色</label>
                                    <input type="color" class="form-control form-control-color form-control-sm" id="gradientStartColor" value="#667eea">
                                </div>
                                <div class="col-6">
                                    <label class="form-label">结束颜色</label>
                                    <input type="color" class="form-control form-control-color form-control-sm" id="gradientEndColor" value="#764ba2">
                                </div>
                            </div>
                        </div>

                        <!-- 图片背景配置 -->
                        <div id="imageBackgroundConfig" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">背景图片</label>
                                <!-- URL输入 -->
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control form-control-sm" id="backgroundImageUrl" placeholder="输入图片URL地址">
                                    <button class="btn btn-outline-secondary btn-sm" type="button" id="loadBackgroundImageUrl">
                                        <i class="bi bi-link"></i> 加载
                                    </button>
                                </div>
                                <small class="text-muted">输入图片URL地址或选择本地图片</small>

                                <!-- 文件上传和选择 -->
                                <div class="d-flex gap-2 mt-2">
                                    <input type="file" class="form-control form-control-sm" id="backgroundImageUpload" accept="image/*">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectBackgroundImage()">
                                        <i class="bi bi-image"></i> 选择已有
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearBackgroundImage()">
                                        <i class="bi bi-x"></i> 清除
                                    </button>
                                </div>

                                <!-- 预览 -->
                                <div id="backgroundImagePreview" class="mt-2" style="display: none;">
                                    <img id="backgroundImagePreviewImg" src="" alt="背景预览" style="max-width: 100%; height: 60px; object-fit: cover; border-radius: 4px;">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">显示模式</label>
                                <select class="form-select form-select-sm" id="backgroundImageMode">
                                    <option value="cover">覆盖 (保持比例，填满画布)</option>
                                    <option value="contain">适应 (保持比例，完整显示)</option>
                                    <option value="stretch">拉伸 (填满画布，可能变形)</option>
                                    <option value="repeat">平铺 (重复显示)</option>
                                    <option value="center">居中 (原始大小，居中显示)</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">图片透明度</label>
                                <div class="d-flex align-items-center gap-2">
                                    <input type="range" class="form-range flex-fill" id="backgroundImageOpacity" min="0" max="100" value="100" step="5">
                                    <span class="text-muted small" id="backgroundImageOpacityValue">100%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 网格配置 -->
                    <div class="property-section">
                        <h6>网格配置</h6>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showCanvasGrid" checked>
                            <label class="form-check-label" for="showCanvasGrid">显示网格</label>
                        </div>
                        <div id="canvasGridConfig">
                            <div class="mb-3">
                                <label class="form-label">网格颜色</label>
                                <input type="color" class="form-control form-control-color form-control-sm" id="canvasGridColor" value="#e0e0e0">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">网格透明度</label>
                                <div class="d-flex align-items-center gap-2">
                                    <input type="range" class="form-range flex-fill" id="canvasGridOpacity" min="10" max="100" value="100" step="5">
                                    <span class="text-muted small" id="canvasGridOpacityValue">100%</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">网格大小 (px)</label>
                                <input type="number" class="form-control form-control-sm" id="canvasGridSize" min="10" max="50" value="20">
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="d-grid gap-2 mt-4">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetCanvasConfig()">
                            <i class="bi bi-arrow-clockwise"></i> 重置为默认
                        </button>
                    </div>
                </div>

                <div id="widgetProperties" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">组件配置</h6>
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-primary btn-sm" onclick="duplicateSelectedWidget()" title="复制组件">
                                <i class="bi bi-copy"></i> 复制组件
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="deleteSelectedWidget()" title="删除组件">
                                <i class="bi bi-trash"></i> 删除组件
                            </button>
                        </div>
                    </div>

                    <!-- 标签页导航 -->
                    <ul class="nav nav-tabs nav-tabs-sm mb-3" id="configTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="properties-tab" data-bs-toggle="tab" data-bs-target="#properties-panel" type="button" role="tab">
                                <i class="bi bi-gear"></i> 组件属性
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="data-tab" data-bs-toggle="tab" data-bs-target="#data-panel" type="button" role="tab">
                                <i class="bi bi-database"></i> 数据配置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="layers-tab" data-bs-toggle="tab" data-bs-target="#layers-panel" type="button" role="tab">
                                <i class="bi bi-layers"></i> 图层管理
                            </button>
                        </li>
                    </ul>

                    <!-- 标签页内容 -->
                    <div class="tab-content" id="configTabContent">
                        <!-- 组件属性面板 -->
                        <div class="tab-pane fade show active" id="properties-panel" role="tabpanel">
                            <!-- 基础属性 -->
                            <div class="property-section">
                                <h6>基础属性</h6>
                                <div class="mb-3">
                                    <label class="form-label">组件标题</label>
                                    <input type="text" class="form-control form-control-sm" id="widgetTitle">
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <label class="form-label">宽度</label>
                                        <input type="number" class="form-control form-control-sm" id="widgetWidth">
                                    </div>
                                    <div class="col-6">
                                        <label class="form-label">高度</label>
                                        <input type="number" class="form-control form-control-sm" id="widgetHeight">
                                    </div>
                                </div>
                            </div>

                            <!-- 样式配置 -->
                            <div class="property-section">
                                <h6>样式配置</h6>

                                <!-- 基础样式 -->
                                <div class="mb-3">
                                    <label class="form-label">预设主题</label>
                                    <select class="form-select form-select-sm" id="chartTheme">
                                        <option value="default">默认主题</option>
                                        <option value="business">商务主题</option>
                                        <option value="tech">科技主题</option>
                                        <option value="elegant">优雅主题</option>
                                        <option value="dark">深色主题</option>
                                        <option value="colorful">多彩主题</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">背景颜色</label>
                                    <input type="color" class="form-control form-control-color form-control-sm" id="backgroundColor" value="#ffffff">
                                </div>
                                <div class="mb-3" style="display: none;">
                                    <label class="form-label">边框颜色</label>
                                    <input type="color" class="form-control form-control-color form-control-sm" id="borderColor" value="#dee2e6">
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="showTitle" checked>
                                    <label class="form-check-label" for="showTitle">显示标题栏</label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="transparent">
                                    <label class="form-check-label" for="transparent">透明背景</label>
                                </div>

                                <!-- 图表样式 -->
                                <div id="chartStyleConfig" style="display: none;">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="showChartTitle" checked>
                                        <label class="form-check-label" for="showChartTitle">显示图表标题</label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="borderlessMode">
                                        <label class="form-check-label" for="borderlessMode">无边框模式</label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">标题字体大小</label>
                                        <input type="number" class="form-control form-control-sm" id="titleFontSize" value="16" min="10" max="30">
                                    </div>

                                    <!-- 文字配置 -->
                                    <h6 class="text-muted mb-2">文字配置</h6>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">数值轴文字大小</label>
                                            <input type="number" class="form-control form-control-sm" id="yAxisFontSize" value="12" min="8" max="20">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">数值轴文字颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="yAxisFontColor" value="#8c8c8c">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">时间轴文字大小</label>
                                            <input type="number" class="form-control form-control-sm" id="xAxisFontSize" value="12" min="8" max="20">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">时间轴文字颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="xAxisFontColor" value="#8c8c8c">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">数据值文字大小</label>
                                            <input type="number" class="form-control form-control-sm" id="dataLabelFontSize" value="11" min="8" max="18">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">数据值文字颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="dataLabelFontColor" value="#5470c6">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">图例文字大小</label>
                                            <input type="number" class="form-control form-control-sm" id="legendFontSize" value="12" min="8" max="18">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">图例文字颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="legendFontColor" value="#666666">
                                        </div>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="showLegend" checked>
                                        <label class="form-check-label" for="showLegend">显示图例</label>
                                    </div>

                                    <!-- 图表显示元素 -->
                                    <h6 class="text-muted mb-2">显示元素</h6>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="showDataLabels">
                                        <label class="form-check-label" for="showDataLabels">数据点显示数值</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="showYAxis" checked>
                                        <label class="form-check-label" for="showYAxis">显示左侧数值轴</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="showYAxisLabels" checked>
                                        <label class="form-check-label" for="showYAxisLabels">显示数值轴标签</label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="showXAxis" checked>
                                        <label class="form-check-label" for="showXAxis">显示下方时间轴</label>
                                    </div>
                                </div>

                                <!-- 折线图特有样式 -->
                                <div id="lineChartStyleConfig" style="display: none;">
                                    <!-- 线条配置 -->
                                    <h6 class="text-muted mb-2">线条配置</h6>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">线条宽度</label>
                                            <input type="number" class="form-control form-control-sm" id="lineWidth" value="3" min="1" max="10">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">线条类型</label>
                                            <select class="form-select form-select-sm" id="lineType">
                                                <option value="solid">实线</option>
                                                <option value="dashed">虚线</option>
                                                <option value="dotted">点线</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">线条颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="lineColor" value="#5470c6">
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="useGradientLine">
                                        <label class="form-check-label" for="useGradientLine">渐变线条</label>
                                    </div>
                                    <div class="mb-3" id="lineGradientConfig" style="display: none;">
                                        <label class="form-label">渐变终止色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="lineGradientColor" value="#91cc75">
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="smoothLine" checked>
                                        <label class="form-check-label" for="smoothLine">平滑曲线</label>
                                    </div>

                                    <!-- 面积配置 -->
                                    <h6 class="text-muted mb-2">面积配置</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="showArea">
                                        <label class="form-check-label" for="showArea">显示面积</label>
                                    </div>
                                    <div id="areaConfig" style="display: none;">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="useGradientArea" checked>
                                            <label class="form-check-label" for="useGradientArea">渐变面积</label>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-4">
                                                <label class="form-label">起始色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="areaStartColor" value="#5470c6">
                                            </div>
                                            <div class="col-4">
                                                <label class="form-label">中间色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="areaMidColor" value="#91cc75">
                                            </div>
                                            <div class="col-4">
                                                <label class="form-label">结束色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="areaEndColor" value="#ffffff">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 数据点配置 -->
                                    <h6 class="text-muted mb-2">数据点配置</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="showSymbol" checked>
                                        <label class="form-check-label" for="showSymbol">显示数据点</label>
                                    </div>
                                    <div id="symbolConfig">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">点类型</label>
                                                <select class="form-select form-select-sm" id="symbolType">
                                                    <option value="circle">圆形</option>
                                                    <option value="rect">方形</option>
                                                    <option value="roundRect">圆角方形</option>
                                                    <option value="triangle">三角形</option>
                                                    <option value="diamond">菱形</option>
                                                </select>
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">点大小</label>
                                                <input type="number" class="form-control form-control-sm" id="symbolSize" value="8" min="4" max="20">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">点颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="symbolColor" value="#5470c6">
                                        </div>
                                    </div>

                                    <!-- 阴影配置 -->
                                    <h6 class="text-muted mb-2">阴影效果</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enableShadow">
                                        <label class="form-check-label" for="enableShadow">启用阴影</label>
                                    </div>
                                    <div id="shadowConfig" style="display: none;">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">阴影模糊</label>
                                                <input type="number" class="form-control form-control-sm" id="shadowBlur" value="10" min="0" max="50">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">阴影偏移</label>
                                                <input type="number" class="form-control form-control-sm" id="shadowOffsetY" value="3" min="0" max="20">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">阴影颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="shadowColor" value="#000000">
                                        </div>
                                    </div>
                                </div>

                                <!-- 柱状图特有样式 -->
                                <div id="barChartStyleConfig" style="display: none;">
                                    <div class="mb-3">
                                        <label class="form-label">柱子宽度(%)</label>
                                        <input type="number" class="form-control form-control-sm" id="barWidth" value="60" min="20" max="100">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">圆角半径</label>
                                        <input type="number" class="form-control form-control-sm" id="borderRadius" value="4" min="0" max="20">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">圆角模式</label>
                                        <select class="form-select form-select-sm" id="borderRadiusMode">
                                            <option value="top">仅顶部圆角</option>
                                            <option value="right">仅右侧圆角</option>
                                            <option value="all">全圆角</option>
                                        </select>
                                    </div>

                                    <!-- 柱体颜色配置 -->
                                    <h6 class="text-muted mb-2">柱体颜色</h6>
                                    <div class="mb-3">
                                        <label class="form-label">柱体颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="barColor" value="#5470c6">
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="useGradientBar">
                                        <label class="form-check-label" for="useGradientBar">渐变柱体</label>
                                    </div>
                                    <div id="barGradientConfig" style="display: none;">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">起始色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="barStartColor" value="#5470c6">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">结束色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="barEndColor" value="#91cc75">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 多折线图特有样式 -->
                                <div id="multiLineChartStyleConfig" style="display: none;">
                                    <!-- 基础样式配置 -->
                                    <h6 class="text-muted mb-3">基础样式</h6>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="multiLineSmooth" checked>
                                        <label class="form-check-label" for="multiLineSmooth">平滑曲线</label>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">线条宽度</label>
                                        <input type="number" class="form-control form-control-sm" id="multiLineWidth" value="2" min="1" max="10">
                                        <small class="form-text text-muted">控制所有折线的粗细程度</small>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="multiLineShowSymbol" checked>
                                        <label class="form-check-label" for="multiLineShowSymbol">显示标记点</label>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">标记点大小</label>
                                        <input type="number" class="form-control form-control-sm" id="multiLineSymbolSize" value="6" min="2" max="20">
                                        <small class="form-text text-muted">控制数据点标记的尺寸</small>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="multiLineShowArea">
                                        <label class="form-check-label" for="multiLineShowArea">面积填充</label>
                                    </div>

                                    <div class="form-check mb-4">
                                        <input class="form-check-input" type="checkbox" id="multiLineEnableDualYAxis">
                                        <label class="form-check-label" for="multiLineEnableDualYAxis">双Y轴模式</label>
                                    </div>

                                    <!-- 各折线样式配置 -->
                                    <h6 class="text-muted mb-3">各折线样式</h6>

                                    <div class="alert alert-info border-0 mb-3" style="background-color: #f8f9fa;">
                                        <i class="bi bi-info-circle me-2"></i>
                                        <small>系统将根据数据集数量自动为每条折线生成样式配置</small>
                                    </div>

                                    <!-- 隐藏的配置项，用于兼容现有逻辑 -->
                                    <input type="hidden" id="useIndividualStyles" value="true">
                                    <input type="hidden" id="lineCount" value="1">

                                    <!-- 动态生成的各折线样式配置容器 -->
                                    <div id="multiLineStylesContainer">
                                        <div id="lineStylesList">
                                            <div class="alert alert-secondary border-0" style="background-color: #f8f9fa;">
                                                <i class="bi bi-palette me-2"></i>
                                                <small>请先配置数据集，系统将自动生成对应的折线样式配置</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 仪表盘特有样式 -->
                                <div id="gaugeStyleConfig" style="display: none;">
                                    <div class="row">
                                        <div class="col-6">
                                            <label class="form-label">最小值</label>
                                            <input type="number" class="form-control form-control-sm" id="gaugeMin" value="0">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">最大值</label>
                                            <input type="number" class="form-control form-control-sm" id="gaugeMax" value="100">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">单位</label>
                                        <input type="text" class="form-control form-control-sm" id="gaugeUnit" value="%">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">半径(%)</label>
                                        <input type="number" class="form-control form-control-sm" id="gaugeRadius" value="80" min="50" max="100">
                                    </div>
                                </div>

                                <!-- 饼图特有样式 -->
                                <div id="pieStyleConfig" style="display: none;">
                                    <!-- 饼图类型 -->
                                    <h6 class="text-muted mb-2">饼图类型</h6>
                                    <div class="mb-3">
                                        <label class="form-label">饼图样式</label>
                                        <select class="form-select form-select-sm" id="pieType">
                                            <option value="ring">环形饼图</option>
                                            <option value="solid">实心饼图</option>
                                        </select>
                                    </div>
                                    <div class="mb-3" id="pieRadiusConfig">
                                        <label class="form-label">饼图半径(%)</label>
                                        <input type="number" class="form-control form-control-sm" id="pieRadius" value="70" min="30" max="90">
                                    </div>
                                    <div class="mb-3" id="pieRingConfig" style="display: none;">
                                        <label class="form-label">环形饼图配置</label>
                                        <div class="mb-2">
                                            <label class="form-label small">内环半径: <span id="innerRadiusValue">40</span>%</label>
                                            <input type="range" class="form-range" id="pieInnerRadius" min="10" max="80" value="40" step="5">
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label small">外环半径: <span id="outerRadiusValue">70</span>%</label>
                                            <input type="range" class="form-range" id="pieOuterRadius" min="30" max="90" value="70" step="5">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">圆角半径</label>
                                        <input type="number" class="form-control form-control-sm" id="pieBorderRadius" value="0" min="0" max="20">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">圆角模式</label>
                                        <select class="form-select form-select-sm" id="pieBorderRadiusMode">
                                            <option value="all">全圆角</option>
                                            <option value="outer">仅最外部圆角</option>
                                        </select>
                                    </div>

                                    <!-- 饼图显示配置 -->
                                    <h6 class="text-muted mb-2">显示配置</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="pieShowDataLabels" checked>
                                        <label class="form-check-label" for="pieShowDataLabels">显示数据标签</label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="showPieBorder" checked>
                                        <label class="form-check-label" for="showPieBorder">显示边线</label>
                                    </div>

                                    <!-- 饼图颜色配置 -->
                                    <h6 class="text-muted mb-2">颜色配置</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="useCustomPieColors">
                                        <label class="form-check-label" for="useCustomPieColors">自定义颜色</label>
                                    </div>
                                    <div id="pieColorConfig" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">颜色数量</label>
                                            <select class="form-select form-select-sm" id="pieColorCount" onchange="onPieColorCountChange()">
                                                <option value="4">4种颜色</option>
                                                <option value="6">6种颜色</option>
                                                <option value="8">8种颜色</option>
                                                <option value="10">10种颜色</option>
                                                <option value="12">12种颜色</option>
                                            </select>
                                        </div>
                                        <div id="pieColorList">
                                            <!-- 动态生成的颜色配置 -->
                                        </div>
                                    </div>
                                </div>

                                <!-- 水波图特有样式 -->
                                <div id="waterStyleConfig" style="display: none;">
                                    <!-- 外框配置 -->
                                    <h6 class="text-muted mb-2">外框配置</h6>
                                    <div class="mb-3">
                                        <label class="form-label">外框厚度</label>
                                        <input type="number" class="form-control form-control-sm" id="waterBorderWidth" value="5" min="0" max="20">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">外框颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="waterBorderColor" value="#1890ff">
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="useWaterBorderGradient">
                                        <label class="form-check-label" for="useWaterBorderGradient">外框渐变</label>
                                    </div>
                                    <div id="waterBorderGradientConfig" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">渐变结束色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="waterBorderGradientColor" value="#52c41a">
                                        </div>
                                    </div>

                                    <!-- 水波配置 -->
                                    <h6 class="text-muted mb-2">水波配置</h6>
                                    <div class="mb-3">
                                        <label class="form-label">水波颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="waterWaveColor" value="#1890ff">
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="useWaterWaveGradient">
                                        <label class="form-check-label" for="useWaterWaveGradient">水波渐变</label>
                                    </div>
                                    <div id="waterWaveGradientConfig" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">渐变结束色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="waterWaveGradientColor" value="#52c41a">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">波浪幅度</label>
                                        <input type="number" class="form-control form-control-sm" id="waterAmplitude" value="20" min="5" max="50">
                                    </div>

                                    <!-- 数值显示配置 -->
                                    <h6 class="text-muted mb-2">数值显示</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="showWaterLabel" checked>
                                        <label class="form-check-label" for="showWaterLabel">显示数值</label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">数值字体大小</label>
                                        <input type="number" class="form-control form-control-sm" id="waterLabelFontSize" value="20" min="10" max="40">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">数值字体颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="waterLabelFontColor" value="#333333">
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="showPercentageSymbol" checked>
                                        <label class="form-check-label" for="showPercentageSymbol">显示百分比符号(%)</label>
                                    </div>
                                </div>

                                <!-- 柱状百分比图特有样式 -->
                                <div id="columnPercentageStyleConfig" style="display: none;">
                                    <!-- 柱子样式配置 -->
                                    <h6 class="text-muted mb-2">柱子样式</h6>
                                    <div class="mb-3">
                                        <label class="form-label">柱子宽度</label>
                                        <input type="number" class="form-control form-control-sm" id="columnBarWidth" value="40" min="10" max="100">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">圆角大小</label>
                                        <input type="number" class="form-control form-control-sm" id="columnBorderRadius" value="8" min="0" max="20">
                                    </div>

                                    <!-- 颜色配置 -->
                                    <h6 class="text-muted mb-2">颜色配置</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="columnUseGradient" checked>
                                        <label class="form-check-label" for="columnUseGradient">启用渐变</label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">起始颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="columnStartColor" value="#4facfe">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">结束颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="columnEndColor" value="#00f2fe">
                                    </div>

                                    <!-- 阴影配置 -->
                                    <h6 class="text-muted mb-2">阴影配置</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="columnEnableShadow" checked>
                                        <label class="form-check-label" for="columnEnableShadow">启用阴影</label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">阴影颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="columnShadowColor" value="#000000">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">阴影模糊度</label>
                                        <input type="number" class="form-control form-control-sm" id="columnShadowBlur" value="10" min="0" max="30">
                                    </div>

                                    <!-- 数值标签配置 -->
                                    <h6 class="text-muted mb-2">数值标签</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="columnShowDataLabel" checked>
                                        <label class="form-check-label" for="columnShowDataLabel">显示数据标签</label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">标签位置</label>
                                        <select class="form-select form-select-sm" id="columnDataLabelPosition">
                                            <option value="vertical-top">垂直顶部</option>
                                            <option value="top">柱体顶部</option>
                                            <option value="center">垂直居中</option>
                                            <option value="inside">柱体中间</option>
                                            <option value="bottom">柱体底部</option>
                                            <option value="vertical-bottom">垂直下方</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">标签颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="columnDataLabelColor" value="#333333">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">标签字体大小</label>
                                        <input type="number" class="form-control form-control-sm" id="columnDataLabelFontSize" value="14" min="8" max="24">
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="columnShowPercentage" checked>
                                        <label class="form-check-label" for="columnShowPercentage">显示百分比</label>
                                    </div>

                                    <!-- 柱状背景配置 -->
                                    <h6 class="text-muted mb-2">柱状背景</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="columnShowBarBackground" checked>
                                        <label class="form-check-label" for="columnShowBarBackground">显示柱状背景</label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">背景颜色</label>
                                        <input type="color" class="form-control form-control-color form-control-sm" id="columnBarBackgroundColor" value="#f5f5f5">
                                    </div>

                                    <!-- 超额显示配置 -->
                                    <h6 class="text-muted mb-2">超额显示</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="columnAllowOverflow" checked>
                                        <label class="form-check-label" for="columnAllowOverflow">允许超额显示</label>
                                        <div class="form-text">允许显示超过100%的进度</div>
                                    </div>
                                </div>

                                <!-- 装饰组件特有样式 -->
                                <div id="decorationStyleConfig" style="display: none;">
                                    <!-- 素材选择 -->
                                    <h6 class="text-muted mb-2">素材选择</h6>
                                    <div class="mb-3">
                                        <label class="form-label">素材类型</label>
                                        <select class="form-select form-select-sm" id="decorationMaterialType">
                                            <option value="">请选择类型</option>
                                            <option value="decoration">装饰素材</option>
                                            <option value="border">边框素材</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">选择素材</label>
                                        <select class="form-select form-select-sm" id="decorationMaterial">
                                            <option value="">请先选择素材类型</option>
                                            <!-- 素材选项将根据类型动态加载 -->
                                        </select>
                                    </div>
                                    <div class="mb-3" id="decorationPreview" style="display: none;">
                                        <label class="form-label">预览</label>
                                        <div class="border rounded p-2 text-center" style="height: 100px; background-color: #f8f9fa;">
                                            <img id="decorationPreviewImg" src="" alt="预览" style="max-width: 100%; max-height: 100%; object-fit: contain;">
                                        </div>
                                    </div>

                                    <!-- 外观配置 -->
                                    <h6 class="text-muted mb-2">外观配置</h6>
                                    <div class="mb-3">
                                        <label class="form-label">缩放模式</label>
                                        <select class="form-select form-select-sm" id="decorationObjectFit">
                                            <option value="contain">适应容器</option>
                                            <option value="cover">覆盖容器</option>
                                            <option value="fill">拉伸填充</option>
                                            <option value="scale-down">缩小适应</option>
                                            <option value="none">原始尺寸</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">透明度: <span id="decorationOpacityValue">100</span>%</label>
                                        <input type="range" class="form-range" id="decorationOpacity" min="0" max="100" value="100" step="5">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">旋转角度: <span id="decorationRotationValue">0</span>°</label>
                                        <input type="range" class="form-range" id="decorationRotation" min="0" max="360" value="0" step="15">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">混合模式</label>
                                        <select class="form-select form-select-sm" id="decorationBlendMode">
                                            <option value="normal">正常</option>
                                            <option value="multiply">正片叠底</option>
                                            <option value="screen">滤色</option>
                                            <option value="overlay">叠加</option>
                                            <option value="soft-light">柔光</option>
                                            <option value="hard-light">强光</option>
                                            <option value="color-dodge">颜色减淡</option>
                                            <option value="color-burn">颜色加深</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- HTML组件特有样式 -->
                                <div id="htmlStyleConfig" style="display: none;">
                                    <!-- HTML样式选择 -->
                                    <h6 class="text-muted mb-2">HTML样式选择</h6>
                                    <div class="mb-3">
                                        <label class="form-label">HTML样式分类</label>
                                        <select class="form-select form-select-sm" id="htmlSnippetCategory">
                                            <option value="">请选择分类</option>
                                            <!-- 分类选项将动态加载 -->
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">选择HTML样式</label>
                                        <select class="form-select form-select-sm" id="htmlSnippetSelect">
                                            <option value="">请先选择分类</option>
                                            <!-- HTML样式选项将根据分类动态加载 -->
                                        </select>
                                    </div>
                                    <div class="mb-3" id="htmlSnippetPreview" style="display: none;">
                                        <label class="form-label">预览</label>
                                        <div class="border rounded p-2" style="height: 120px; background-color: #f8f9fa; overflow: hidden;">
                                            <iframe id="htmlSnippetPreviewFrame" src="" style="width: 100%; height: 100%; border: none; transform: scale(0.8); transform-origin: top left;"></iframe>
                                        </div>
                                    </div>

                                    <!-- 显示配置 -->
                                    <h6 class="text-muted mb-2">显示配置</h6>

                                    <div class="mb-3">
                                        <label class="form-label">透明度: <span id="htmlOpacityValue">100</span>%</label>
                                        <input type="range" class="form-range" id="htmlOpacity" min="0" max="100" value="100" step="5">
                                    </div>
                                </div>

                                <!-- 文本组件特有样式 -->
                                <div id="textStyleConfig" style="display: none;">
                                    <!-- 字体配置 -->
                                    <h6 class="text-muted mb-2">字体配置</h6>
                                    <div class="mb-3">
                                        <label class="form-label">字体系列</label>
                                        <select class="form-select form-select-sm" id="textFontFamily">
                                            <option value="Arial, sans-serif">Arial</option>
                                            <option value="'Microsoft YaHei', sans-serif">微软雅黑</option>
                                            <option value="'SimHei', sans-serif">黑体</option>
                                            <option value="'SimSun', serif">宋体</option>
                                            <option value="'KaiTi', serif">楷体</option>
                                            <option value="'Courier New', monospace">Courier New</option>
                                            <option value="'Times New Roman', serif">Times New Roman</option>
                                        </select>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">字体大小</label>
                                            <input type="number" class="form-control form-control-sm" id="textFontSize" value="16" min="8" max="72">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">字体颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="textFontColor" value="#333333">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">字体粗细</label>
                                            <select class="form-select form-select-sm" id="textFontWeight">
                                                <option value="normal">正常</option>
                                                <option value="bold">粗体</option>
                                                <option value="lighter">细体</option>
                                                <option value="100">100</option>
                                                <option value="200">200</option>
                                                <option value="300">300</option>
                                                <option value="400">400 (正常)</option>
                                                <option value="500">500</option>
                                                <option value="600">600</option>
                                                <option value="700">700 (粗体)</option>
                                                <option value="800">800</option>
                                                <option value="900">900</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">字体样式</label>
                                            <select class="form-select form-select-sm" id="textFontStyle">
                                                <option value="normal">正常</option>
                                                <option value="italic">斜体</option>
                                                <option value="oblique">倾斜</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">文字装饰</label>
                                        <select class="form-select form-select-sm" id="textDecoration">
                                            <option value="none">无</option>
                                            <option value="underline">下划线</option>
                                            <option value="overline">上划线</option>
                                            <option value="line-through">删除线</option>
                                        </select>
                                    </div>

                                    <!-- 布局配置 -->
                                    <h6 class="text-muted mb-2">布局配置</h6>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">水平对齐</label>
                                            <select class="form-select form-select-sm" id="textAlign">
                                                <option value="left">左对齐</option>
                                                <option value="center">居中</option>
                                                <option value="right">右对齐</option>
                                                <option value="justify">两端对齐</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">垂直对齐</label>
                                            <select class="form-select form-select-sm" id="textVerticalAlign">
                                                <option value="top">顶部</option>
                                                <option value="middle">居中</option>
                                                <option value="bottom">底部</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">行高</label>
                                            <input type="number" class="form-control form-control-sm" id="textLineHeight" value="1.5" min="0.8" max="3" step="0.1">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">字间距</label>
                                            <input type="number" class="form-control form-control-sm" id="textLetterSpacing" value="0" min="-2" max="10" step="0.1">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">内边距</label>
                                        <input type="number" class="form-control form-control-sm" id="textPadding" value="10" min="0" max="50">
                                    </div>

                                    <!-- 背景渐变配置 -->
                                    <h6 class="text-muted mb-2">背景渐变</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enableTextGradient">
                                        <label class="form-check-label" for="enableTextGradient">启用背景渐变</label>
                                    </div>
                                    <div id="textGradientConfig" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">渐变类型</label>
                                            <select class="form-select form-select-sm" id="textGradientType">
                                                <option value="linear">线性渐变</option>
                                                <option value="radial">径向渐变</option>
                                            </select>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">起始颜色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="textGradientStartColor" value="#ffffff">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">结束颜色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="textGradientEndColor" value="#f0f0f0">
                                            </div>
                                        </div>
                                        <div class="mb-3" id="textGradientAngleConfig">
                                            <label class="form-label">渐变角度</label>
                                            <input type="range" class="form-range" id="textGradientAngle" min="0" max="360" value="0">
                                            <div class="form-text">角度: <span id="textGradientAngleValue">0</span>°</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">圆角半径</label>
                                            <input type="range" class="form-range" id="textBorderRadius" min="0" max="50" value="0">
                                            <div class="form-text">圆角: <span id="textBorderRadiusValue">0</span>px</div>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <!-- 表格组件样式配置 -->
                            <div id="tableStyleConfig" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">表格组件样式配置</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 表格基础样式 -->
                                        <h6 class="text-muted mb-2">表格基础样式</h6>
                                        <div class="mb-3">
                                            <label class="form-label">表格样式</label>
                                            <select class="form-select form-select-sm" id="tableStyle">
                                                <option value="table">基础表格</option>
                                                <option value="table-striped">斑马纹表格</option>
                                                <option value="table-bordered">边框表格</option>
                                                <option value="table-hover">悬停效果表格</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">表格大小</label>
                                            <select class="form-select form-select-sm" id="tableSize">
                                                <option value="table-sm">紧凑</option>
                                                <option value="table-normal" selected>标准</option>
                                                <option value="table-lg">宽松</option>
                                            </select>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="showTableHeader" checked>
                                            <label class="form-check-label" for="showTableHeader">显示表头</label>
                                        </div>

                                        <!-- 表格颜色配置 -->
                                        <h6 class="text-muted mb-2">颜色配置</h6>
                                        <div class="mb-3">
                                            <label class="form-label">表头背景色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="tableHeaderBgColor" value="#f8f9fa">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">表头文字颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="tableHeaderTextColor" value="#212529">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">表格背景色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="tableBodyBgColor" value="#ffffff">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">表格文字颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="tableBodyTextColor" value="#212529">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">边框颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="tableBorderColor" value="#dee2e6">
                                        </div>

                                        <!-- 字体配置 -->
                                        <h6 class="text-muted mb-2">字体配置</h6>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">表头字体大小</label>
                                                <input type="number" class="form-control form-control-sm" id="tableHeaderFontSize" value="14" min="10" max="24">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">表格字体大小</label>
                                                <input type="number" class="form-control form-control-sm" id="tableBodyFontSize" value="12" min="10" max="20">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">字体粗细</label>
                                            <select class="form-select form-select-sm" id="tableFontWeight">
                                                <option value="normal">正常</option>
                                                <option value="bold">粗体</option>
                                                <option value="lighter">细体</option>
                                            </select>
                                        </div>

                                        <!-- 轮播配置 -->
                                        <h6 class="text-muted mb-2">数据轮播</h6>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="enableTableCarousel">
                                            <label class="form-check-label" for="enableTableCarousel">启用数据轮播</label>
                                        </div>
                                        <div id="tableCarouselConfig" style="display: none;">
                                            <div class="row mb-3">
                                                <div class="col-6">
                                                    <label class="form-label">每页显示行数</label>
                                                    <input type="number" class="form-control form-control-sm" id="tableRowsPerPage" value="5" min="1" max="20">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">轮播间隔(秒)</label>
                                                    <input type="number" class="form-control form-control-sm" id="tableCarouselInterval" value="3" min="1" max="10">
                                                </div>
                                            </div>
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="showTablePagination">
                                                <label class="form-check-label" for="showTablePagination">显示分页指示器</label>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>

                            <!-- 图片组件样式配置 -->
                            <div id="imageStyleConfig" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">图片组件配置</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 图片源配置 -->
                                        <h6 class="text-muted mb-2">图片源</h6>
                                        <div class="mb-3">
                                            <label class="form-label">图片地址</label>
                                            <input type="url" class="form-control form-control-sm" id="imageUrl" placeholder="请输入图片URL地址">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">或上传图片</label>
                                            <input type="file" class="form-control form-control-sm" id="imageUpload" accept="image/*">
                                            <div class="form-text">支持 JPG、PNG、GIF 等格式</div>
                                        </div>
                                        <div class="mb-3" id="imagePreview" style="display: none;">
                                            <label class="form-label">预览</label>
                                            <div class="border rounded p-2 text-center">
                                                <img id="imagePreviewImg" src="" alt="图片预览" style="max-width: 100%; max-height: 100px; object-fit: contain;">
                                            </div>
                                        </div>

                                        <!-- 外观配置 -->
                                        <h6 class="text-muted mb-2">外观配置</h6>
                                        <div class="mb-3">
                                            <label class="form-label">圆角半径</label>
                                            <input type="range" class="form-range" id="imageBorderRadius" min="0" max="50" value="0">
                                            <div class="form-text">圆角: <span id="imageBorderRadiusValue">0</span>px</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">旋转角度</label>
                                            <input type="range" class="form-range" id="imageRotation" min="0" max="360" value="0">
                                            <div class="form-text">角度: <span id="imageRotationValue">0</span>°</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">透明度</label>
                                            <input type="range" class="form-range" id="imageOpacity" min="0" max="100" value="100">
                                            <div class="form-text">透明度: <span id="imageOpacityValue">100</span>%</div>
                                        </div>

                                        <!-- 尺寸配置 -->
                                        <h6 class="text-muted mb-2">尺寸配置</h6>
                                        <div class="mb-3">
                                            <label class="form-label">图片适应方式</label>
                                            <select class="form-select form-select-sm" id="imageObjectFit">
                                                <option value="contain">适应容器</option>
                                                <option value="cover">填充容器</option>
                                                <option value="fill">拉伸填充</option>
                                                <option value="none">原始尺寸</option>
                                                <option value="scale-down">缩小适应</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 超链接组件样式配置 -->
                            <div id="hyperlinkStyleConfig" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">超链接组件配置</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 超链接配置 -->
                                        <h6 class="text-muted mb-2">超链接配置</h6>
                                        <div class="mb-3">
                                            <label class="form-label">链接地址</label>
                                            <input type="url" class="form-control form-control-sm" id="hyperlinkUrl" placeholder="请输入链接URL地址，如：https://www.example.com">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">链接文本</label>
                                            <input type="text" class="form-control form-control-sm" id="hyperlinkText" placeholder="请输入链接显示文本" value="点击跳转">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">打开方式</label>
                                            <select class="form-select form-select-sm" id="hyperlinkTarget">
                                                <option value="_self">当前窗口打开</option>
                                                <option value="_blank">新标签页打开</option>
                                            </select>
                                        </div>

                                        <!-- 文字样式配置 -->
                                        <h6 class="text-muted mb-2">文字样式</h6>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">字体大小</label>
                                                <input type="number" class="form-control form-control-sm" id="hyperlinkFontSize" value="16" min="8" max="72">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">字体颜色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="hyperlinkFontColor" value="#007bff">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">字体粗细</label>
                                                <select class="form-select form-select-sm" id="hyperlinkFontWeight">
                                                    <option value="normal">正常</option>
                                                    <option value="bold">粗体</option>
                                                    <option value="lighter">细体</option>
                                                    <option value="500">中等</option>
                                                    <option value="600">半粗体</option>
                                                </select>
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">文本对齐</label>
                                                <select class="form-select form-select-sm" id="hyperlinkTextAlign">
                                                    <option value="left">左对齐</option>
                                                    <option value="center">居中</option>
                                                    <option value="right">右对齐</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">字体样式</label>
                                                <select class="form-select form-select-sm" id="hyperlinkFontStyle">
                                                    <option value="normal">正常</option>
                                                    <option value="italic">斜体</option>
                                                </select>
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">文本装饰</label>
                                                <select class="form-select form-select-sm" id="hyperlinkTextDecoration">
                                                    <option value="none">无</option>
                                                    <option value="underline">下划线</option>
                                                    <option value="overline">上划线</option>
                                                    <option value="line-through">删除线</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- 外观配置 -->
                                        <h6 class="text-muted mb-2">外观配置</h6>
                                        <div class="mb-3">
                                            <label class="form-label">透明度</label>
                                            <input type="range" class="form-range" id="hyperlinkOpacity" min="0" max="100" value="100">
                                            <div class="form-text">透明度: <span id="hyperlinkOpacityValue">100</span>%</div>
                                        </div>
                                        <div class="alert alert-info alert-sm">
                                            <i class="bi bi-info-circle me-1"></i>
                                            <small>背景颜色和边框颜色请在上方的"样式配置"中设置</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 视频组件样式配置 -->
                            <div id="videoStyleConfig" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">视频组件配置</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 视频源配置 -->
                                        <h6 class="text-muted mb-2">视频源配置</h6>
                                        <div class="mb-3">
                                            <label class="form-label">视频URL</label>
                                            <input type="url" class="form-control form-control-sm" id="videoUrl" placeholder="https://example.com/video.mp4">
                                            <div class="form-text">
                                                <i class="bi bi-info-circle"></i>
                                                支持MP4、WebM、OGV格式的直链视频
                                            </div>
                                        </div>

                                        <!-- 播放控制 -->
                                        <h6 class="text-muted mb-2">播放控制</h6>
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="videoAutoplay">
                                                <label class="form-check-label" for="videoAutoplay">
                                                    自动播放
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="videoLoop">
                                                <label class="form-check-label" for="videoLoop">
                                                    循环播放
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="videoMuted" checked>
                                                <label class="form-check-label" for="videoMuted">
                                                    静音播放
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="videoControls" checked>
                                                <label class="form-check-label" for="videoControls">
                                                    显示控制条
                                                </label>
                                            </div>
                                        </div>

                                        <!-- 视频样式 -->
                                        <h6 class="text-muted mb-2">视频样式</h6>
                                        <div class="mb-3">
                                            <label class="form-label">封面图片</label>
                                            <input type="url" class="form-control form-control-sm" id="videoPoster" placeholder="https://example.com/poster.jpg">
                                            <div class="form-text">
                                                <i class="bi bi-info-circle"></i>
                                                视频加载前显示的封面图片
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">圆角大小</label>
                                                <input type="number" class="form-control form-control-sm" id="videoBorderRadius" value="0" min="0" max="50">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">透明度</label>
                                                <input type="range" class="form-range" id="videoOpacity" min="0" max="100" value="100">
                                                <div class="form-text">透明度: <span id="videoOpacityValue">100</span>%</div>
                                            </div>
                                        </div>

                                        <!-- 边框样式 -->
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">边框宽度</label>
                                                <input type="number" class="form-control form-control-sm" id="videoBorderWidth" value="0" min="0" max="10">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">边框颜色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="videoBorderColor" value="#dee2e6">
                                            </div>
                                        </div>

                                        <!-- 阴影效果 -->
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="videoShadow">
                                                <label class="form-check-label" for="videoShadow">
                                                    阴影效果
                                                </label>
                                            </div>
                                        </div>

                                        <div class="alert alert-info alert-sm">
                                            <i class="bi bi-info-circle me-1"></i>
                                            <small>视频组件仅支持直链URL，不支持嵌入式播放器代码</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 时间组件样式配置 -->
                            <div id="timeStyleConfig" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">时间组件配置</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 时间格式配置 -->
                                        <h6 class="text-muted mb-2">时间格式</h6>
                                        <div class="mb-3">
                                            <label class="form-label">显示格式</label>
                                            <select class="form-select form-select-sm" id="timeComponentFormat">
                                                <option value="YYYY-MM-DD HH:mm:ss">2024-12-25 14:30:45</option>
                                                <option value="YYYY/MM/DD HH:mm:ss">2024/12/25 14:30:45</option>
                                                <option value="MM/DD/YYYY HH:mm:ss">12/25/2024 14:30:45</option>
                                                <option value="DD/MM/YYYY HH:mm:ss">25/12/2024 14:30:45</option>
                                                <option value="YYYY-MM-DD HH:mm">2024-12-25 14:30</option>
                                                <option value="HH:mm:ss">14:30:45</option>
                                                <option value="hh:mm:ss A">02:30:45 PM</option>
                                                <option value="YYYY年MM月DD日 HH:mm:ss">2024年12月25日 14:30:45</option>
                                                <option value="MM月DD日 HH:mm">12月25日 14:30</option>
                                            </select>
                                        </div>

                                        <!-- 字体样式 -->
                                        <h6 class="text-muted mb-2">字体样式</h6>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">字体大小</label>
                                                <input type="number" class="form-control form-control-sm" id="timeFontSize" value="24" min="8" max="200">
                                                <div class="form-text">像素(px)</div>
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">字体粗细</label>
                                                <select class="form-select form-select-sm" id="timeFontWeight">
                                                    <option value="normal">正常</option>
                                                    <option value="bold">粗体</option>
                                                    <option value="lighter">细体</option>
                                                    <option value="bolder">特粗</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">字体样式</label>
                                            <select class="form-select form-select-sm" id="timeFontStyle">
                                                <option value="normal">正常</option>
                                                <option value="italic">斜体</option>
                                            </select>
                                        </div>

                                        <!-- 颜色配置 -->
                                        <h6 class="text-muted mb-2">颜色配置</h6>
                                        <div class="mb-3">
                                            <label class="form-label">颜色类型</label>
                                            <select class="form-select form-select-sm" id="timeColorType">
                                                <option value="solid">纯色</option>
                                                <option value="gradient">渐变色</option>
                                            </select>
                                        </div>

                                        <!-- 纯色配置 -->
                                        <div id="timeSolidColorConfig" class="mb-3">
                                            <label class="form-label">文字颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="timeColor" value="#333333">
                                        </div>

                                        <!-- 渐变色配置 -->
                                        <div id="timeGradientColorConfig" style="display: none;">
                                            <div class="row mb-3">
                                                <div class="col-6">
                                                    <label class="form-label">起始颜色</label>
                                                    <input type="color" class="form-control form-control-color form-control-sm" id="timeGradientStart" value="#667eea">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">结束颜色</label>
                                                    <input type="color" class="form-control form-control-color form-control-sm" id="timeGradientEnd" value="#764ba2">
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">渐变方向</label>
                                                <select class="form-select form-select-sm" id="timeGradientDirection">
                                                    <option value="to right">从左到右</option>
                                                    <option value="to left">从右到左</option>
                                                    <option value="to bottom">从上到下</option>
                                                    <option value="to top">从下到上</option>
                                                    <option value="45deg">对角线(左上到右下)</option>
                                                    <option value="135deg">对角线(左下到右上)</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- 特效配置 -->
                                        <h6 class="text-muted mb-2">特效配置</h6>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">透明度</label>
                                                <input type="range" class="form-range" id="timeOpacity" min="0" max="100" value="100">
                                                <div class="form-text">透明度: <span id="timeOpacityValue">100</span>%</div>
                                            </div>
                                            <div class="col-6">
                                                <div class="form-check form-switch mt-4">
                                                    <input class="form-check-input" type="checkbox" id="timeTextShadow">
                                                    <label class="form-check-label" for="timeTextShadow">
                                                        文字阴影
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="alert alert-info alert-sm">
                                            <i class="bi bi-info-circle me-1"></i>
                                            <small>时间组件会自动每秒更新显示当前时间</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 状态指示器组件样式配置 -->
                            <div id="statusIndicatorStyleConfig" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">状态指示器配置</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 形状设置 -->
                                        <h6 class="text-muted mb-2">形状设置</h6>
                                        <div class="mb-3">
                                            <label class="form-label">指示器形状</label>
                                            <select class="form-select form-select-sm" id="statusIndicatorShape">
                                                <option value="circle">圆形</option>
                                                <option value="square">方形</option>
                                                <option value="rectangle">长方形</option>
                                                <option value="diamond">菱形</option>
                                            </select>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">指示器大小</label>
                                                <input type="number" class="form-control form-control-sm" id="statusIndicatorSize" value="60" min="20" max="200">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">边框宽度</label>
                                                <input type="number" class="form-control form-control-sm" id="statusIndicatorBorderWidth" value="2" min="0" max="10">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">边框颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="statusIndicatorBorderColor" value="#cccccc">
                                        </div>

                                        <!-- 条件设置 -->
                                        <h6 class="text-muted mb-2">条件设置</h6>
                                        <!-- 条件1 -->
                                        <div class="border rounded p-3 mb-3">
                                            <h6 class="text-success mb-2">条件1 (正常)</h6>
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <label class="form-label">最小值</label>
                                                    <input type="number" class="form-control form-control-sm" id="statusIndicatorCondition1Min" value="0">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">最大值</label>
                                                    <input type="number" class="form-control form-control-sm" id="statusIndicatorCondition1Max" value="30">
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <label class="form-label">条件名称</label>
                                                    <input type="text" class="form-control form-control-sm" id="statusIndicatorCondition1Name" value="正常">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">颜色</label>
                                                    <input type="color" class="form-control form-control-color form-control-sm" id="statusIndicatorCondition1Color" value="#28a745">
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 条件2 -->
                                        <div class="border rounded p-3 mb-3">
                                            <h6 class="text-warning mb-2">条件2 (警告)</h6>
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <label class="form-label">最小值</label>
                                                    <input type="number" class="form-control form-control-sm" id="statusIndicatorCondition2Min" value="31">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">最大值</label>
                                                    <input type="number" class="form-control form-control-sm" id="statusIndicatorCondition2Max" value="70">
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <label class="form-label">条件名称</label>
                                                    <input type="text" class="form-control form-control-sm" id="statusIndicatorCondition2Name" value="警告">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">颜色</label>
                                                    <input type="color" class="form-control form-control-color form-control-sm" id="statusIndicatorCondition2Color" value="#ffc107">
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 条件3 -->
                                        <div class="border rounded p-3 mb-3">
                                            <h6 class="text-danger mb-2">条件3 (危险)</h6>
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <label class="form-label">最小值</label>
                                                    <input type="number" class="form-control form-control-sm" id="statusIndicatorCondition3Min" value="71">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">最大值</label>
                                                    <input type="number" class="form-control form-control-sm" id="statusIndicatorCondition3Max" value="100">
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <label class="form-label">条件名称</label>
                                                    <input type="text" class="form-control form-control-sm" id="statusIndicatorCondition3Name" value="危险">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">颜色</label>
                                                    <input type="color" class="form-control form-control-color form-control-sm" id="statusIndicatorCondition3Color" value="#dc3545">
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 离线状态 -->
                                        <div class="mb-3">
                                            <label class="form-label">离线状态颜色</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="statusIndicatorOfflineColor" value="#6c757d">
                                        </div>

                                        <!-- 显示设置 -->
                                        <h6 class="text-muted mb-2">显示设置</h6>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="statusIndicatorShowConditionName" checked>
                                            <label class="form-check-label" for="statusIndicatorShowConditionName">显示条件名称</label>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <label class="form-label">字体大小</label>
                                                <input type="number" class="form-control form-control-sm" id="statusIndicatorFontSize" value="12" min="8" max="24">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">字体颜色</label>
                                                <input type="color" class="form-control form-control-color form-control-sm" id="statusIndicatorFontColor" value="#333333">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">字体粗细</label>
                                            <select class="form-select form-select-sm" id="statusIndicatorFontWeight">
                                                <option value="normal">正常</option>
                                                <option value="bold">粗体</option>
                                            </select>
                                        </div>

                                        <!-- 动画设置 -->
                                        <h6 class="text-muted mb-2">动画设置</h6>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="statusIndicatorEnableAnimation" checked>
                                            <label class="form-check-label" for="statusIndicatorEnableAnimation">启用动画</label>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">动画时长(ms)</label>
                                            <input type="number" class="form-control form-control-sm" id="statusIndicatorAnimationDuration" value="300" min="100" max="1000">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 配置提示 -->
                            <div class="property-section">
                                <div class="alert alert-info alert-sm p-2 mb-3">
                                    <i class="bi bi-info-circle"></i> 配置修改后自动生效
                                </div>
                            </div>
                        </div>

                        <!-- 数据配置面板 -->
                        <div class="tab-pane fade" id="data-panel" role="tabpanel">
                            <!-- 图片组件专用提示 -->
                            <div id="imageDataNotice" class="property-section" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>图片组件说明</strong>
                                    <p class="mb-0 mt-2">图片组件用于显示静态图片，无需配置数据源。请在"组件属性"标签页中配置图片地址和样式。</p>
                                </div>
                            </div>

                            <!-- 超链接组件专用提示 -->
                            <div id="hyperlinkDataNotice" class="property-section" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>超链接组件说明</strong>
                                    <p class="mb-0 mt-2">超链接组件用于显示静态超链接，无需配置数据源。请在"组件属性"标签页中配置超链接地址和样式。</p>
                                </div>
                            </div>

                            <!-- 视频组件专用提示 -->
                            <div id="videoDataNotice" class="property-section" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>视频组件说明</strong>
                                    <p class="mb-0 mt-2">视频组件用于播放URL链接的视频，无需配置数据源。请在"组件属性"标签页中配置视频URL和播放设置。</p>
                                </div>
                            </div>

                            <!-- 时间组件专用提示 -->
                            <div id="timeDataNotice" class="property-section" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>时间组件说明</strong>
                                    <p class="mb-0 mt-2">时间组件用于显示实时系统时间，无需配置数据源。请在"组件属性"标签页中配置时间格式和显示样式。</p>
                                </div>
                            </div>

                            <div class="property-section" id="normalDataConfig">
                                <h6>数据源配置</h6>
                                <div class="mb-3">
                                    <label class="form-label">数据源类型</label>
                                    <select class="form-select form-select-sm" id="dataSourceType" onchange="onDataSourceTypeChange()">
                                        <option value="dataItem">监控项数据</option>
                                        <option value="static">静态数据</option>
                                        <option value="multiData" id="multiDataOption" style="display: none;">多数据源</option>
                                        <option value="externalData">外部数据源</option>
                                    </select>
                                </div>

                                <div id="dataItemConfig">
                                    <div class="mb-3">
                                        <label class="form-label">选择设备</label>
                                        <select class="form-select form-select-sm" id="deviceSelect" onchange="onDeviceChange()">
                                            <option value="">请选择设备</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">选择监控项</label>
                                        <select class="form-select form-select-sm" id="dataItemSelect">
                                            <option value="">请先选择设备</option>
                                        </select>
                                    </div>
                                    <div class="mb-3" id="dataModeConfig">
                                        <label class="form-label">数据模式</label>
                                        <select class="form-select form-select-sm" id="dataMode" onchange="onDataModeChange()">
                                            <option value="realtime">实时数据</option>
                                            <option value="history">历史数据</option>
                                        </select>
                                    </div>
                                    <div class="mb-3" id="historyCountConfig" style="display: none;">
                                        <label class="form-label">历史数据条数</label>
                                        <select class="form-select form-select-sm" id="historyCount">
                                            <option value="5">最新5条</option>
                                            <option value="7">最新7条</option>
                                            <option value="10">最新10条</option>
                                            <option value="20">最新20条</option>
                                            <option value="50" selected>最新50条</option>
                                            <option value="100">最新100条</option>
                                            <option value="200">最新200条</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">时间显示格式</label>
                                        <select class="form-select form-select-sm" id="timeFormat">
                                            <option value="datetime">月日时间 (12-25 14:30)</option>
                                            <option value="date">仅月日 (12-25)</option>
                                            <option value="time">仅时间 (14:30)</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 水波图目标值配置 -->
                                <div id="waterTargetDataConfig" style="display: none;">
                                    <h6 class="text-muted mb-2">目标值配置</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enableWaterTarget">
                                        <label class="form-check-label" for="enableWaterTarget">启用目标值模式</label>
                                    </div>
                                    <div id="waterTargetConfig" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">目标值来源</label>
                                            <select class="form-select form-select-sm" id="waterTargetSource">
                                                <option value="manual">手动输入</option>
                                                <option value="dataItem">监控项数据</option>
                                            </select>
                                        </div>
                                        <div class="mb-3" id="waterTargetManualConfig">
                                            <label class="form-label">目标值</label>
                                            <input type="number" class="form-control form-control-sm" id="waterTargetValue" value="100" min="0">
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="showTargetValues" checked>
                                            <label class="form-check-label" for="showTargetValues">显示目标值数值</label>
                                            <div class="form-text">不勾选时只显示百分比</div>
                                        </div>
                                        <div id="waterTargetDataItemConfig" style="display: none;">
                                            <div class="mb-3">
                                                <label class="form-label">目标值设备</label>
                                                <select class="form-select form-select-sm" id="waterTargetDevice">
                                                    <option value="">请选择设备</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">目标值监控项</label>
                                                <select class="form-select form-select-sm" id="waterTargetDataItem">
                                                    <option value="">请先选择设备</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 柱状百分比图目标值配置 -->
                                <div id="columnTargetDataConfig" style="display: none;">
                                    <h6 class="text-muted mb-2">目标值配置</h6>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enableColumnTarget">
                                        <label class="form-check-label" for="enableColumnTarget">启用目标值模式</label>
                                    </div>
                                    <div id="columnTargetConfig" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">目标值来源</label>
                                            <select class="form-select form-select-sm" id="columnTargetSource">
                                                <option value="manual">手动输入</option>
                                                <option value="dataItem">监控项数据</option>
                                            </select>
                                        </div>
                                        <div class="mb-3" id="columnTargetManualConfig">
                                            <label class="form-label">目标值</label>
                                            <input type="number" class="form-control form-control-sm" id="columnTargetValue" value="80" min="0">
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="showColumnTargetLabel" checked>
                                            <label class="form-check-label" for="showColumnTargetLabel">显示目标值标签</label>
                                            <div class="form-text">在目标线旁显示数值</div>
                                        </div>
                                        <div id="columnTargetDataItemConfig" style="display: none;">
                                            <div class="mb-3">
                                                <label class="form-label">目标值设备</label>
                                                <select class="form-select form-select-sm" id="columnTargetDevice">
                                                    <option value="">请选择设备</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">目标值监控项</label>
                                                <select class="form-select form-select-sm" id="columnTargetDataItem">
                                                    <option value="">请先选择设备</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 静态数据配置 -->
                                <div id="staticDataConfig" style="display: none;">
                                    <div class="mb-3">
                                        <label class="form-label">数据标签</label>
                                        <textarea class="form-control form-control-sm" id="staticLabels" rows="3" placeholder="请输入标签，每行一个">一月
二月
三月
四月
五月
六月</textarea>
                                        <div class="form-text">每行一个标签，用于X轴或饼图分类</div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">数据数值</label>
                                        <textarea class="form-control form-control-sm" id="staticValues" rows="3" placeholder="请输入数值，每行一个">120
200
150
80
70
110</textarea>
                                        <div class="form-text">每行一个数值，与标签对应</div>
                                    </div>
                                </div>

                                <!-- 外部数据源配置 -->
                                <div id="externalDataConfig" style="display: none;">
                                    <!-- 多数据集开关 -->
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="multiExternalDataSetEnabled" onchange="onMultiExternalDataSetToggle()">
                                            <label class="form-check-label" for="multiExternalDataSetEnabled">
                                                启用多数据集模式
                                            </label>
                                        </div>
                                        <div class="form-text">
                                            <i class="bi bi-info-circle"></i>
                                            启用后可以同时使用多个数据集，支持数据合并显示
                                        </div>
                                    </div>

                                    <!-- 单数据集配置 -->
                                    <div id="singleExternalDataSetConfig">
                                        <div class="mb-3">
                                            <label class="form-label">选择数据集</label>
                                            <select class="form-select form-select-sm" id="dataSetSelect" onchange="applyPropertiesRealTime()">
                                                <option value="">请选择数据集</option>
                                                <!-- 数据集选项将动态加载 -->
                                            </select>
                                            <div class="form-text">
                                                <i class="bi bi-info-circle"></i>
                                                选择已创建的外部数据集作为数据源
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3" id="dataSetInfo" style="display: none;">
                                        <label class="form-label">数据集信息</label>
                                        <div class="card">
                                            <div class="card-body p-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <div class="fw-bold" id="dataSetName">-</div>
                                                        <small class="text-muted" id="dataSetDescription">-</small>
                                                    </div>
                                                    <div class="text-end">
                                                        <small class="text-muted" id="dataSetSource">-</small>
                                                        <div>
                                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshDataSetData()" title="刷新数据">
                                                                <i class="bi bi-arrow-clockwise"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 图表组件字段选择配置 - 隐藏，系统自动选择字段 -->
                                    <div class="mb-3" id="dataSetFieldConfig" style="display: none;">
                                        <!-- 隐藏的字段选择器，保留用于自动选择功能 -->
                                        <select class="form-select form-select-sm" id="labelFieldSelect" onchange="applyPropertiesRealTime()" style="display: none;">
                                            <option value="">请选择标签字段</option>
                                        </select>
                                        <select class="form-select form-select-sm" id="valueFieldSelect" onchange="applyPropertiesRealTime()" style="display: none;">
                                            <option value="">请选择数值字段</option>
                                        </select>

                                        <!-- 显示自动选择的字段信息 -->
                                        <div id="autoSelectedFieldsInfo" style="display: none;">
                                            <label class="form-label">字段配置</label>
                                            <div class="alert alert-info py-2">
                                                <i class="bi bi-check-circle text-success"></i>
                                                <strong>系统已自动选择合适的字段：</strong>
                                                <div class="mt-1">
                                                    <small>
                                                        <span class="badge bg-primary me-2">标签字段</span>
                                                        <span id="selectedLabelFieldInfo">-</span>
                                                    </small>
                                                </div>
                                                <div class="mt-1">
                                                    <small>
                                                        <span class="badge bg-success me-2">数值字段</span>
                                                        <span id="selectedValueFieldInfo">-</span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 表格组件字段配置 -->
                                    <div class="mb-3" id="tableFieldConfig" style="display: none;">
                                        <label class="form-label">表格字段配置</label>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted">配置表格显示的字段和对应的数据源字段</small>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTableField()">
                                                <i class="bi bi-plus"></i> 添加字段
                                            </button>
                                        </div>
                                        <div id="tableFieldList">
                                            <!-- 动态生成的表格字段配置 -->
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="bi bi-info-circle"></i>
                                                至少需要配置一个字段，配置后表格将自动更新
                                            </small>
                                        </div>
                                    </div>

                                    <!-- 多数据集配置 -->
                                    <div id="multiExternalDataSetConfig" style="display: none;">
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label class="form-label mb-0">数据集配置</label>
                                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addExternalDataSet()">
                                                    <i class="bi bi-plus"></i> 添加数据集
                                                </button>
                                            </div>
                                            <div class="form-text mb-3">
                                                <i class="bi bi-info-circle"></i>
                                                可以添加多个数据集，系统将自动合并数据进行显示
                                            </div>
                                        </div>

                                        <!-- 数据合并策略 -->
                                        <div class="mb-3">
                                            <label class="form-label">数据合并策略</label>
                                            <select class="form-select form-select-sm" id="dataMergeStrategy" onchange="applyPropertiesRealTime()">
                                                <option value="union">联合模式 - 合并所有数据</option>
                                                <option value="separate">分离模式 - 保持数据集独立</option>
                                            </select>
                                            <div class="form-text">
                                                <i class="bi bi-info-circle"></i>
                                                联合模式将所有数据集的数据合并显示，分离模式保持各数据集独立显示
                                            </div>
                                        </div>

                                        <!-- 动态数据集列表容器 -->
                                        <div id="pieExternalDataSourceList" class="multi-external-dataset-container">
                                            <!-- 饼图多外部数据集配置将动态生成 -->
                                        </div>
                                        <div id="barExternalDataSourceList" class="multi-external-dataset-container">
                                            <!-- 柱状图多外部数据集配置将动态生成 -->
                                        </div>
                                        <div id="horizontalBarExternalDataSourceList" class="multi-external-dataset-container">
                                            <!-- 水平柱状图多外部数据集配置将动态生成 -->
                                        </div>
                                        <div id="lineExternalDataSourceList" class="multi-external-dataset-container">
                                            <!-- 折线图多外部数据集配置将动态生成 -->
                                        </div>
                                        <div id="multiLineExternalDataSourceList" class="multi-external-dataset-container">
                                            <!-- 多折线图多外部数据集配置将动态生成 -->
                                        </div>
                                        <div id="tableExternalDataSourceList" class="multi-external-dataset-container">
                                            <!-- 表格多外部数据集配置将动态生成 -->
                                        </div>
                                    </div>
                                </div>

                                <!-- 饼图多数据源配置 -->
                                <div id="pieMultiDataConfig" style="display: none;">
                                    <h6 class="text-muted mb-2">多数据源配置</h6>
                                    <div class="mb-3">
                                        <label class="form-label">数据源数量</label>
                                        <select class="form-select form-select-sm" id="pieDataSourceCount" onchange="onPieDataSourceCountChange()">
                                            <option value="1">1个数据源</option>
                                            <option value="2">2个数据源</option>
                                            <option value="3">3个数据源</option>
                                            <option value="4">4个数据源</option>
                                            <option value="5">5个数据源</option>
                                            <option value="6">6个数据源</option>
                                            <option value="7">7个数据源</option>
                                            <option value="8">8个数据源</option>
                                            <option value="9">9个数据源</option>
                                            <option value="10">10个数据源</option>
                                        </select>
                                    </div>
                                    <div id="pieDataSourceList">
                                        <!-- 动态生成的数据源配置 -->
                                    </div>
                                </div>

                                <!-- 柱状图多数据源配置 -->
                                <div id="barMultiDataConfig" style="display: none;">
                                    <h6 class="text-muted mb-2">多数据源配置</h6>
                                    <div class="mb-3">
                                        <label class="form-label">数据源数量</label>
                                        <select class="form-select form-select-sm" id="barDataSourceCount" onchange="onBarDataSourceCountChange()">
                                            <option value="1">1个数据源</option>
                                            <option value="2">2个数据源</option>
                                            <option value="3">3个数据源</option>
                                            <option value="4">4个数据源</option>
                                            <option value="5">5个数据源</option>
                                            <option value="6">6个数据源</option>
                                            <option value="7">7个数据源</option>
                                            <option value="8">8个数据源</option>
                                            <option value="9">9个数据源</option>
                                            <option value="10">10个数据源</option>
                                        </select>
                                    </div>
                                    <div id="barDataSourceList">
                                        <!-- 动态生成的数据源配置 -->
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">自动刷新间隔(秒)</label>
                                    <select class="form-select form-select-sm" id="refreshInterval">
                                        <option value="1">1秒 (高频)</option>
                                        <option value="2">2秒</option>
                                        <option value="5" selected>5秒 (推荐)</option>
                                        <option value="10">10秒</option>
                                        <option value="30">30秒</option>
                                        <option value="60">1分钟</option>
                                        <option value="300">5分钟</option>
                                    </select>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle"></i>
                                        组件将根据此间隔自动刷新数据，配置修改后立即生效
                                    </div>
                                </div>

                                <!-- 数值转换配置 -->
                                <h6 class="text-muted mb-2 mt-3">数值转换</h6>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableDataTransform">
                                    <label class="form-check-label" for="enableDataTransform">启用数值转换</label>
                                </div>
                                <div id="dataTransformConfig" style="display: none;">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">运算类型</label>
                                            <select class="form-select form-select-sm" id="transformOperation">
                                                <option value="add">加法 (+)</option>
                                                <option value="subtract">减法 (-)</option>
                                                <option value="multiply">乘法 (×)</option>
                                                <option value="divide">除法 (÷)</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">运算数值</label>
                                            <input type="number" class="form-control form-control-sm" id="transformValue" value="1" step="0.01">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">小数位数</label>
                                            <select class="form-select form-select-sm" id="decimalPlaces">
                                                <option value="0">0位</option>
                                                <option value="1">1位</option>
                                                <option value="2" selected>2位</option>
                                                <option value="3">3位</option>
                                                <option value="4">4位</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">后缀符号</label>
                                            <input type="text" class="form-control form-control-sm" id="dataSuffix" placeholder="如: °C, kg, %">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">转换示例</label>
                                        <div class="form-text" id="transformExample">原始值: 5050 → 转换后: 50.50°C</div>
                                    </div>
                                </div>
                            </div> <!-- 关闭normalDataConfig -->
                        </div>

                        <!-- 图层管理面板 -->
                        <div class="tab-pane fade" id="layers-panel" role="tabpanel">
                            <div class="property-section">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">图层列表</h6>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-secondary" onclick="moveLayerToTop()" title="置顶">
                                            <i class="bi bi-arrow-up-square"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="moveLayerToBottom()" title="置底">
                                            <i class="bi bi-arrow-down-square"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="form-text mb-3">拖拽图层项可调整显示顺序，上方图层会覆盖下方图层</div>
                                <div id="layersList" class="layers-list">
                                    <!-- 图层项将动态生成 -->
                                </div>
                            </div>
                        </div>
                </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 缩放级别选择弹窗 -->
    <div class="dropdown-menu" id="zoomOptionsMenu" style="display: none; position: absolute; z-index: 1050;">
        <h6 class="dropdown-header">缩放级别</h6>
        <button class="dropdown-item" onclick="setZoomLevel(0.25)">25%</button>
        <button class="dropdown-item" onclick="setZoomLevel(0.5)">50%</button>
        <button class="dropdown-item" onclick="setZoomLevel(0.75)">75%</button>
        <button class="dropdown-item" onclick="setZoomLevel(1.0)">100%</button>
        <button class="dropdown-item" onclick="setZoomLevel(1.25)">125%</button>
        <button class="dropdown-item" onclick="setZoomLevel(1.5)">150%</button>
        <button class="dropdown-item" onclick="setZoomLevel(2.0)">200%</button>
        <div class="dropdown-divider"></div>
        <button class="dropdown-item" onclick="zoomToFit()">
            <i class="bi bi-arrows-fullscreen me-2"></i>适应窗口
        </button>
    </div>

    <!-- 隐藏的数据 -->
    <script th:inline="javascript">
        window.dashboardData = {
            id: /*[[${dashboard.id}]]*/ 0,
            name: /*[[${dashboard.name}]]*/ '',
            canvasConfig: /*[[${dashboard.canvasConfig}]]*/ '{"width":1920,"height":1080,"backgroundColor":"#ffffff"}'
        };
    </script>

    <script src="/js/bootstrap.bundle.min.js"></script>
    <script src="/js/chart.min.js"></script>
    <!-- Apache ECharts -->
    <script src="/js/echarts.min.js"></script>
    <!-- ECharts LiquidFill Plugin for Water Charts -->
    <script src="/js/echarts-liquidfill.min.js"></script>
    <script src="/js/bi-widget-configs.js?v=20250730-remove-shapes"></script>
    <script src="/js/bi-echarts-components.js?v=20250127"></script>
    <script src="/js/bi-data-source-manager.js?v=20250127"></script>
    <script src="/js/bi-status-indicator.js?v=20250730-fixed-shapes"></script>
    <script src="/js/bi-dashboard-designer.js?v=20250127-gauge-fix"></script>

    <!-- 标签页功能初始化 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 确保Bootstrap标签页功能正常工作
            const triggerTabList = document.querySelectorAll('#configTabs button[data-bs-toggle="tab"]');
            triggerTabList.forEach(triggerEl => {
                const tabTrigger = new bootstrap.Tab(triggerEl);

                triggerEl.addEventListener('click', event => {
                    event.preventDefault();
                    tabTrigger.show();
                });
            });

            console.log('标签页功能已初始化');

            // 初始化BiDataSourceManager（标准化方式）
            if (typeof initializeBiDataSourceManager === 'function') {
                const initResult = initializeBiDataSourceManager({
                    enableLogging: true,
                    enableValidation: true,
                    enableContextManagement: true
                });

                if (initResult.success) {
                    console.log('设计页面 - BiDataSourceManager初始化成功');
                } else {
                    console.error('设计页面 - BiDataSourceManager初始化失败:', initResult.message);
                    // 降级处理：尝试直接创建实例
                    if (typeof BiDataSourceManager !== 'undefined' && !window.biDataSourceManager) {
                        try {
                            window.biDataSourceManager = new BiDataSourceManager();
                            console.log('设计页面 - BiDataSourceManager降级初始化成功');
                        } catch (error) {
                            console.error('设计页面 - BiDataSourceManager降级初始化也失败:', error);
                        }
                    }
                }
            } else {
                console.warn('设计页面 - initializeBiDataSourceManager函数未定义，使用降级初始化');
                if (typeof BiDataSourceManager !== 'undefined' && !window.biDataSourceManager) {
                    try {
                        window.biDataSourceManager = new BiDataSourceManager();
                        console.log('设计页面 - BiDataSourceManager降级初始化成功');
                    } catch (error) {
                        console.error('设计页面 - BiDataSourceManager降级初始化失败:', error);
                    }
                }
            }

            // 验证初始化结果
            if (typeof checkBiDataSourceManagerAvailability === 'function') {
                const availability = checkBiDataSourceManagerAvailability();
                console.log('设计页面 - BiDataSourceManager可用性检查:', availability);
            }
        });
    </script>

    <!-- 背景图片选择模态框 -->
    <div class="modal fade" id="backgroundImageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择背景图片</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row" id="backgroundImageList">
                        <!-- 图片列表将动态加载 -->
                    </div>
                    <div id="backgroundImageLoading" class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载图片...</p>
                    </div>
                    <div id="backgroundImageEmpty" class="text-center p-4 text-muted" style="display: none;">
                        <i class="bi bi-image" style="font-size: 2rem;"></i>
                        <p class="mt-2">暂无可用图片</p>
                        <a href="/file-manager" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-upload"></i> 上传图片
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
