<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大屏模板管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .template-card {
            transition: transform 0.2s;
        }
        .template-card:hover {
            transform: translateY(-2px);
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #0d6efd;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .progress-container {
            display: none;
        }
        .alert-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 400px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <h2><i class="bi bi-collection"></i> 大屏模板管理</h2>
                <p class="text-muted">导入和导出大屏模板，包含所有资源文件和配置信息</p>
            </div>
        </div>

        <!-- 操作面板 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-upload"></i> 导入模板</h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea">
                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                            <h5 class="mt-3">拖拽文件到此处或点击选择</h5>
                            <p class="text-muted">支持 .zip 格式的模板文件</p>
                            <input type="file" id="templateFile" accept=".zip" style="display: none;">
                            <button type="button" class="btn btn-primary" onclick="document.getElementById('templateFile').click()">
                                选择文件
                            </button>
                        </div>
                        
                        <!-- 导入选项 -->
                        <div class="mt-3" id="importOptions" style="display: none;">
                            <h6>导入选项</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="dashboardName" class="form-label">大屏名称</label>
                                        <input type="text" class="form-control" id="dashboardName" placeholder="留空使用原名称">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="conflictStrategy" class="form-label">冲突处理策略</label>
                                        <select class="form-select" id="conflictStrategy">
                                            <option value="SKIP">跳过冲突文件</option>
                                            <option value="REPLACE">替换现有文件</option>
                                            <option value="RENAME">重命名新文件</option>
                                            <option value="COMPARE_CONTENT">比较内容决定</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="replaceExistingCategories">
                                        <label class="form-check-label" for="replaceExistingCategories">
                                            替换现有分类
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="replaceExistingFiles">
                                        <label class="form-check-label" for="replaceExistingFiles">
                                            替换现有文件
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="replaceExistingHtmlSnippets">
                                        <label class="form-check-label" for="replaceExistingHtmlSnippets">
                                            替换现有HTML代码片段
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="createMissingCategories" checked>
                                        <label class="form-check-label" for="createMissingCategories">
                                            创建缺失的分类
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-success" id="importBtn">
                                    <i class="bi bi-upload"></i> 开始导入
                                </button>
                                <button type="button" class="btn btn-secondary" id="cancelBtn">
                                    取消
                                </button>
                            </div>
                        </div>
                        
                        <!-- 进度条 -->
                        <div class="progress-container mt-3">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2 text-center">
                                <small class="text-muted" id="progressText">准备导入...</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-download"></i> 导出模板</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="dashboardSelect" class="form-label">选择要导出的大屏</label>
                            <select class="form-select" id="dashboardSelect">
                                <option value="">请选择大屏...</option>
                            </select>
                        </div>
                        <button type="button" class="btn btn-primary" id="exportBtn">
                            <i class="bi bi-download"></i> 导出模板
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近操作记录 -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-clock-history"></i> 操作记录</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>操作类型</th>
                                        <th>大屏名称</th>
                                        <th>状态</th>
                                        <th>详情</th>
                                    </tr>
                                </thead>
                                <tbody id="operationHistory">
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">暂无操作记录</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示消息容器 -->
    <div class="alert-container" id="alertContainer"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboards();
            initializeUploadArea();
        });

        // 加载大屏列表
        function loadDashboards() {
            fetch('/api/dashboard/list')
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('dashboardSelect');
                    select.innerHTML = '<option value="">请选择大屏...</option>';
                    
                    if (data && data.length > 0) {
                        data.forEach(dashboard => {
                            const option = document.createElement('option');
                            option.value = dashboard.id;
                            option.textContent = dashboard.name;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载大屏列表失败:', error);
                    showAlert('加载大屏列表失败', 'danger');
                });
        }

        // 初始化上传区域
        function initializeUploadArea() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('templateFile');
            
            // 拖拽事件
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelect(files[0]);
                }
            });
            
            // 文件选择事件
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleFileSelect(e.target.files[0]);
                }
            });
            
            // 导入按钮事件
            document.getElementById('importBtn').addEventListener('click', importTemplate);
            document.getElementById('cancelBtn').addEventListener('click', cancelImport);
            
            // 导出按钮事件
            document.getElementById('exportBtn').addEventListener('click', exportTemplate);
        }

        // 处理文件选择
        function handleFileSelect(file) {
            if (!file.name.toLowerCase().endsWith('.zip')) {
                showAlert('请选择ZIP格式的模板文件', 'warning');
                return;
            }
            
            document.getElementById('importOptions').style.display = 'block';
            showAlert(`已选择文件: ${file.name}`, 'info');
        }

        // 导入模板
        function importTemplate() {
            const fileInput = document.getElementById('templateFile');
            const file = fileInput.files[0];

            if (!file) {
                showAlert('请先选择模板文件', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            // 添加导入选项
            const dashboardName = document.getElementById('dashboardName').value;
            const conflictStrategy = document.getElementById('conflictStrategy').value;
            const replaceExistingCategories = document.getElementById('replaceExistingCategories').checked;
            const replaceExistingFiles = document.getElementById('replaceExistingFiles').checked;
            const replaceExistingHtmlSnippets = document.getElementById('replaceExistingHtmlSnippets').checked;
            const createMissingCategories = document.getElementById('createMissingCategories').checked;

            if (dashboardName) formData.append('dashboardName', dashboardName);
            formData.append('conflictStrategy', conflictStrategy);
            formData.append('replaceExistingCategories', replaceExistingCategories);
            formData.append('replaceExistingFiles', replaceExistingFiles);
            formData.append('replaceExistingHtmlSnippets', replaceExistingHtmlSnippets);
            formData.append('createMissingCategories', createMissingCategories);

            // 显示进度条
            showProgress('正在导入模板...');

            fetch('/api/template/import', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideProgress();

                if (data.success) {
                    showAlert(`模板导入成功！新大屏ID: ${data.dashboardId}`, 'success');
                    addOperationRecord('导入', data.dashboardName, '成功', `大屏ID: ${data.dashboardId}`);
                    resetImportForm();
                    loadDashboards(); // 重新加载大屏列表
                } else {
                    showAlert(`导入失败: ${data.message}`, 'danger');
                    addOperationRecord('导入', file.name, '失败', data.message);
                }
            })
            .catch(error => {
                hideProgress();
                console.error('导入失败:', error);
                showAlert('导入失败，请检查网络连接', 'danger');
                addOperationRecord('导入', file.name, '失败', '网络错误');
            });
        }

        // 导出模板
        function exportTemplate() {
            const dashboardId = document.getElementById('dashboardSelect').value;

            if (!dashboardId) {
                showAlert('请先选择要导出的大屏', 'warning');
                return;
            }

            const dashboardName = document.getElementById('dashboardSelect').selectedOptions[0].text;

            // 显示进度
            showProgress('正在导出模板...');

            // 创建下载链接
            const downloadUrl = `/api/template/export/${dashboardId}`;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `dashboard_template_${dashboardId}.zip`;

            // 监听下载完成
            link.addEventListener('click', function() {
                setTimeout(() => {
                    hideProgress();
                    showAlert('模板导出成功', 'success');
                    addOperationRecord('导出', dashboardName, '成功', `大屏ID: ${dashboardId}`);
                }, 1000);
            });

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 取消导入
        function cancelImport() {
            resetImportForm();
        }

        // 重置导入表单
        function resetImportForm() {
            document.getElementById('templateFile').value = '';
            document.getElementById('dashboardName').value = '';
            document.getElementById('conflictStrategy').value = 'SKIP';
            document.getElementById('replaceExistingCategories').checked = false;
            document.getElementById('replaceExistingFiles').checked = false;
            document.getElementById('replaceExistingHtmlSnippets').checked = false;
            document.getElementById('createMissingCategories').checked = true;
            document.getElementById('importOptions').style.display = 'none';
        }

        // 显示进度条
        function showProgress(text) {
            document.querySelector('.progress-container').style.display = 'block';
            document.getElementById('progressText').textContent = text;
            document.querySelector('.progress-bar').style.width = '50%';
        }

        // 隐藏进度条
        function hideProgress() {
            document.querySelector('.progress-container').style.display = 'none';
            document.querySelector('.progress-bar').style.width = '0%';
        }

        // 显示提示消息
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();

            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" id="${alertId}" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            alertContainer.insertAdjacentHTML('beforeend', alertHtml);

            // 自动消失
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const alert = new bootstrap.Alert(alertElement);
                    alert.close();
                }
            }, 5000);
        }

        // 添加操作记录
        function addOperationRecord(type, name, status, details) {
            const tbody = document.getElementById('operationHistory');

            // 如果是第一条记录，清除占位符
            if (tbody.children.length === 1 && tbody.children[0].children.length === 1) {
                tbody.innerHTML = '';
            }

            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');

            const statusClass = status === '成功' ? 'text-success' : 'text-danger';
            const statusIcon = status === '成功' ? 'bi-check-circle' : 'bi-x-circle';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${timeStr}</td>
                <td><span class="badge bg-${type === '导入' ? 'primary' : 'info'}">${type}</span></td>
                <td>${name}</td>
                <td><i class="bi ${statusIcon} ${statusClass}"></i> ${status}</td>
                <td><small class="text-muted">${details}</small></td>
            `;

            tbody.insertBefore(row, tbody.firstChild);

            // 限制记录数量
            if (tbody.children.length > 10) {
                tbody.removeChild(tbody.lastChild);
            }
        }
    </script>
</body>
</html>
